<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\CharacterBindingService;
use Illuminate\Http\Request;

/**
 * 角色绑定关系管理
 * @deprecated 此控制器已废弃，请使用CharacterController中的角色绑定功能
 * 保留此控制器仅为向后兼容，将在下个版本中移除
 */
class CharacterBindingController extends Controller
{
    protected $bindingService;

    public function __construct(CharacterBindingService $bindingService)
    {
        $this->bindingService = $bindingService;
    }

    /**
     * @ApiTitle (绑定角色)
     * @ApiSummary (用户绑定角色)
     * @ApiMethod (POST)
     * @ApiRoute (/api/characters/bind)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="character_id", type="int", required=true, description="角色ID")
     * @ApiParams (name="binding_name", type="string", required=false, description="绑定名称")
     * @ApiParams (name="custom_description", type="string", required=false, description="自定义描述")
     * @ApiParams (name="custom_config", type="object", required=false, description="自定义配置")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="绑定信息")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "角色绑定成功",
     *   "data": {
     *     "binding_id": 123,
     *     "character_id": 1,
     *     "character_name": "小樱",
     *     "binding_name": "我的小樱",
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function bindCharacter(Request $request)
    {
        $rules = [
            'character_id' => 'required|integer|exists:character_library,id',
            'binding_name' => 'sometimes|string|max:100',
            'custom_description' => 'sometimes|string|max:1000',
            'custom_config' => 'sometimes|array'
        ];

        $messages = [
            'character_id.required' => '角色ID不能为空',
            'character_id.exists' => '角色不存在',
            'binding_name.max' => '绑定名称不能超过100个字符',
            'custom_description.max' => '自定义描述不能超过1000个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        // 使用旧版本方法保持向后兼容
        $result = $this->bindingService->bindCharacterLegacy(
            $user->id,
            $request->character_id,
            $request->binding_name,
            $request->custom_description,
            $request->custom_config ?? []
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (解绑角色)
     * @ApiSummary (用户解绑角色)
     * @ApiMethod (DELETE)
     * @ApiRoute (/api/characters/unbind/{id})
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="绑定ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "角色解绑成功",
     *   "data": {
     *     "binding_id": 123,
     *     "unbind_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function unbindCharacter(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->bindingService->unbindCharacter($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (我的角色绑定列表)
     * @ApiSummary (获取用户的角色绑定列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/characters/my-bindings)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="is_favorite", type="boolean", required=false, description="是否收藏")
     * @ApiParams (name="category_id", type="int", required=false, description="分类筛选")
     * @ApiParams (name="sort", type="string", required=false, description="排序方式：usage,rating,created")
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "bindings": [
     *       {
     *         "id": 123,
     *         "character": {
     *           "id": 1,
     *           "name": "小樱",
     *           "avatar": "https://example.com/avatar.jpg"
     *         },
     *         "binding_name": "我的小樱",
     *         "is_favorite": true,
     *         "usage_count": 10,
     *         "last_used_at": "2024-01-01 12:00:00",
     *         "user_rating": 5.0
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 10,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function getMyBindings(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $filters = [
            'is_favorite' => $request->get('is_favorite'),
            'category_id' => $request->get('category_id'),
            'sort' => $request->get('sort', 'usage')
        ];

        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 20), 100);

        $result = $this->bindingService->getUserBindings($user->id, $filters, $page, $perPage);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (更新角色绑定)
     * @ApiSummary (更新角色绑定配置)
     * @ApiMethod (PUT)
     * @ApiRoute (/api/characters/bindings/{id})
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="绑定ID")
     * @ApiParams (name="binding_name", type="string", required=false, description="绑定名称")
     * @ApiParams (name="custom_description", type="string", required=false, description="自定义描述")
     * @ApiParams (name="custom_config", type="object", required=false, description="自定义配置")
     * @ApiParams (name="is_favorite", type="boolean", required=false, description="是否收藏")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="更新后的绑定信息")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "绑定更新成功",
     *   "data": {
     *     "binding_id": 123,
     *     "binding_name": "我的小樱",
     *     "is_favorite": true,
     *     "updated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function updateBinding(Request $request, $id)
    {
        $rules = [
            'binding_name' => 'sometimes|string|max:100',
            'custom_description' => 'sometimes|string|max:1000',
            'custom_config' => 'sometimes|array',
            'is_favorite' => 'sometimes|boolean'
        ];

        $messages = [
            'binding_name.max' => '绑定名称不能超过100个字符',
            'custom_description.max' => '自定义描述不能超过1000个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $updateData = $request->only([
            'binding_name', 'custom_description', 'custom_config', 'is_favorite'
        ]);

        $result = $this->bindingService->updateBinding($id, $user->id, $updateData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (角色评分)
     * @ApiSummary (对绑定的角色进行评分)
     * @ApiMethod (POST)
     * @ApiRoute (/api/characters/bindings/{id}/rate)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="绑定ID")
     * @ApiParams (name="rating", type="float", required=true, description="评分(1-5)")
     * @ApiParams (name="feedback", type="string", required=false, description="评价反馈")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="评分结果")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "评分成功",
     *   "data": {
     *     "binding_id": 123,
     *     "user_rating": 5.0,
     *     "character_rating": 4.5,
     *     "rated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function rateCharacter(Request $request, $id)
    {
        $rules = [
            'rating' => 'required|numeric|min:1|max:5',
            'feedback' => 'sometimes|string|max:500'
        ];

        $messages = [
            'rating.required' => '评分不能为空',
            'rating.min' => '评分不能低于1分',
            'rating.max' => '评分不能高于5分',
            'feedback.max' => '反馈不能超过500个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->bindingService->rateCharacter(
            $id,
            $user->id,
            $request->rating,
            $request->feedback
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
