<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use App\Helpers\ApiTokenHelper;
use App\Exceptions\ApiException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
//use Illuminate\Support\Facades\Cache;

class AuthService
{
    /**
     * @param string $username
     * @param string $password
     * @return array
     */
    public function register(string $username, string $password)
    {
        // 检查用户名是否已存在（只检查用户名，不检查密码）
        $existingUser = DB::table('users')->select('id')
            ->where('username', $username)
            ->first();

        if (!empty($existingUser)) {
            $code = ApiCodeEnum::USER_ALREADY_EXISTS;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code, 'message' => $message, 'data' => []];
        }

        try {
            // 使用事务确保数据一致性
            DB::beginTransaction();

            $id = DB::table('users')->insertGetId([
                'username' => $username,
                'password' => $password,
                'points' => 0.00,
                'frozen_points' => 0.00,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            $token = ApiTokenHelper::generateToken($id);
            $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);
            Redis::setex('user:token:'.$id, $ttl, ApiTokenHelper::encryptToken($token));

            DB::commit();

            $code = ApiCodeEnum::SUCCESS;
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => ApiCodeEnum::getDescription($code),
                'data' => [
                    'id' => $id,
                    'token' => $token
                ]
            ];
        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();

            // 处理唯一约束冲突
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $code = ApiCodeEnum::USER_ALREADY_EXISTS;
                $message = ApiCodeEnum::getDescription($code);
                return ['code' => $code, 'message' => $message, 'data' => []];
            }

            // 其他数据库错误
            $code = ApiCodeEnum::SYSTEM_ERROR;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code, 'message' => $message, 'data' => []];
        } catch (\Exception $e) {
            DB::rollBack();

            // 系统错误
            $code = ApiCodeEnum::ERROR;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code, 'message' => $message, 'data' => []];
        }
    }

    /**
     * @param int $username
     * @param int $password
     * @return array
     */
    public function login(string $username, string $password)
    {
        $user = DB::table('users')->select('id')
            ->where('username', $username)
            ->where('password', $password)
            ->first();
        if(empty($user))
        {
            $code = ApiCodeEnum::USER_NOT_REGISTERED;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code,'message' => $message,'data' => []];
        }else{
            $id = $user->id;
            $code = ApiCodeEnum::SUCCESS;
            $token = ApiTokenHelper::generateToken($id);
            $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);

            $redisKey = 'user:token:'.$id;
            $encryptedToken = ApiTokenHelper::encryptToken($token);
            Redis::setex($redisKey, $ttl, $encryptedToken);

            return ['code' => $code, 'message' => 'success', 'data' => ['token' => $token, 'user' => ['id' => $id]]];
        }
    }

    /**
     * 通过邮箱登录 (符合dev-api-guidelines-add.mdc标准)
     * @param string $email
     * @param string $password
     * @return array
     */
    public function loginByEmail(string $email, string $password)
    {
        $user = DB::table('users')->select('id', 'username', 'email', 'nickname')
            ->where('email', $email)
            ->where('password', $password)
            ->where('status', 1)
            ->first();

        if(empty($user))
        {
            $code = ApiCodeEnum::USER_NOT_REGISTERED;
            $message = ApiCodeEnum::getDescription($code);
            return ['code' => $code,'message' => $message,'data' => []];
        }else{
            $id = $user->id;
            $code = ApiCodeEnum::SUCCESS;
            $token = ApiTokenHelper::generateToken($id);
            $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);

            $redisKey = 'user:token:'.$id;
            $encryptedToken = ApiTokenHelper::encryptToken($token);
            Redis::setex($redisKey, $ttl, $encryptedToken);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $id,
                        'username' => $user->username,
                        'email' => $user->email,
                        'nickname' => $user->nickname
                    ]
                ]
            ];
        }
    }

    /**
     * @param $token
     * @return int
     */
    public function getAuthUserID($token)
    {
        $id = ApiTokenHelper::getUserIdByToken($token);
        if(!empty($id))
        {
            $encrypt_token = Redis::get('user:token:'.$id);
            if($encrypt_token == ApiTokenHelper::encryptToken($token))
            {
                return $id;
            }
        }
        throw new ApiException('Token无效,请从新登录',ApiCodeEnum::INVALID_TOKEN);
    }

    /**
     * 从请求中提取token
     *
     * @param Request $request
     * @return string|null
     */
    public static function extractToken(Request $request): ?string
    {
        // 首先尝试从请求参数中获取token
        $token = $request->input('token');

        if (empty($token)) {
            // 从Authorization头中提取token
            $header = $request->header('Authorization', '');
            $position = strrpos($header, 'Bearer ');
            if ($position !== false) {
                $header = substr($header, $position + 7);
                $token = strpos($header, ',') !== false ? strstr($header, ',', true) : $header;
            }
        }

        return $token ?: null;
    }

    /**
     * 验证token并返回用户信息
     *
     * @param string $token
     * @return object|null 返回用户信息对象或null
     */
    public static function validateToken(string $token): ?object
    {
        // 解析用户ID
        $userId = ApiTokenHelper::getUserIdByToken($token);
        if (empty($userId)) {
            return null;
        }

        // 验证token是否有效 - 使用与存储时相同的键格式
        $redisKey = 'user:token:' . $userId;
        $encryptToken = Redis::get($redisKey);
        if ($encryptToken !== ApiTokenHelper::encryptToken($token)) {
            return null;
        }

        // 查询用户信息
        $user = DB::table('users')->where('id', $userId)->first();
        if (!$user) {
            return null;
        }

        return $user;
    }

    /**
     * 认证用户并返回用户信息或错误响应
     *
     * @param Request $request
     * @return array 返回 ['success' => bool, 'user' => object|null, 'response' => array|null]
     */
    public static function authenticate(Request $request): array
    {
        // 提取token
        $token = self::extractToken($request);
        if (empty($token)) {
            return [
                'success' => false,
                'user' => null,
                'response' => [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '请登录后操作',
                    'data' => []
                ]
            ];
        }

        // 验证token
        $user = self::validateToken($token);
        if (!$user) {
            return [
                'success' => false,
                'user' => null,
                'response' => [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '请登录后操作',
                    'data' => []
                ]
            ];
        }

        return [
            'success' => true,
            'user' => $user,
            'response' => null
        ];
    }

    /**
     * 快速认证方法，直接返回用户信息或抛出认证失败响应
     *
     * @param Request $request
     * @return object 用户信息对象
     * @throws \Exception 认证失败时抛出异常，包含响应数据
     */
    public static function requireAuth(Request $request): object
    {
        $result = self::authenticate($request);

        if (!$result['success']) {
            throw new \Exception(json_encode($result['response']));
        }

        return $result['user'];
    }

}
