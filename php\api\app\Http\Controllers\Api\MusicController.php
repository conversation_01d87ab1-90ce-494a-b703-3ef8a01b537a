<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\MusicService;
use Illuminate\Http\Request;

/**
 * AI音乐生成与批量创作
 */
class MusicController extends Controller
{
    protected $musicService;

    public function __construct(MusicService $musicService)
    {
        $this->musicService = $musicService;
    }

    /**
     * @ApiTitle (音乐生成)
     * @ApiSummary (使用AI生成音乐)
     * @ApiMethod (POST)
     * @ApiRoute (/api/music/generate)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="prompt", type="string", required=true, description="音乐生成提示词")
     * @ApiParams (name="genre", type="string", required=false, description="音乐风格：pop/rock/classical/jazz/electronic")
     * @ApiParams (name="mood", type="string", required=false, description="音乐情绪：happy/sad/energetic/calm/dramatic")
     * @ApiParams (name="duration", type="int", required=false, description="音乐时长（秒）")
     * @ApiParams (name="tempo", type="string", required=false, description="节拍：slow/medium/fast")
     * @ApiParams (name="instruments", type="array", required=false, description="乐器列表")
     * @ApiParams (name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams (name="platform", type="string", required=false, description="指定AI平台：minimax/volcengine")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "音乐生成任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "estimated_cost": "0.2000",
     *     "estimated_duration": 120,
     *     "platform": "minimax"
     *   }
     * })
     */
    public function generate(Request $request)
    {
        $rules = [
            'prompt' => 'required|string|min:5|max:1000',
            'genre' => 'sometimes|string|in:pop,rock,classical,jazz,electronic,ambient,folk,country',
            'mood' => 'sometimes|string|in:happy,sad,energetic,calm,dramatic,romantic,mysterious,epic',
            'duration' => 'sometimes|integer|min:10|max:300',
            'tempo' => 'sometimes|string|in:slow,medium,fast',
            'instruments' => 'sometimes|array',
            'instruments.*' => 'string|max:50',
            'project_id' => 'sometimes|integer|exists:projects,id',
            'platform' => 'sometimes|string|in:minimax,volcengine'
        ];

        $messages = [
            'prompt.required' => '音乐生成提示词不能为空',
            'prompt.min' => '音乐生成提示词至少5个字符',
            'prompt.max' => '音乐生成提示词不能超过1000个字符',
            'genre.in' => '音乐风格必须是：pop、rock、classical、jazz、electronic、ambient、folk、country之一',
            'mood.in' => '音乐情绪必须是：happy、sad、energetic、calm、dramatic、romantic、mysterious、epic之一',
            'duration.min' => '音乐时长至少10秒',
            'duration.max' => '音乐时长不能超过300秒',
            'tempo.in' => '节拍必须是：slow、medium、fast之一',
            'project_id.exists' => '项目不存在',
            'platform.in' => 'AI平台必须是：minimax、volcengine之一'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $generationParams = [
            'genre' => $request->get('genre', 'pop'),
            'mood' => $request->get('mood', 'happy'),
            'duration' => $request->get('duration', 60),
            'tempo' => $request->get('tempo', 'medium'),
            'instruments' => $request->get('instruments', []),
            'platform' => $request->get('platform', 'minimax')
        ];

        $result = $this->musicService->generateMusic(
            $user->id,
            $request->prompt,
            $request->project_id,
            $generationParams
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (音乐生成状态查询)
     * @ApiSummary (查询音乐生成任务的状态和结果)
     * @ApiMethod (GET)
     * @ApiRoute (/api/music/{id}/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="任务ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "music_generation",
     *     "status": "completed",
     *     "platform": "minimax",
     *     "audio_url": "https://aiapi.tiptop.cn/music/generated/123.mp3",
     *     "duration": 60,
     *     "file_size": "3.2MB",
     *     "cost": "0.2000",
     *     "processing_time_ms": 120000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:02:00"
     *   }
     * })
     */
    public function getStatus(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->musicService->getMusicStatus($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (音乐生成结果获取)
     * @ApiSummary (获取音乐生成的详细结果和下载信息)
     * @ApiMethod (GET)
     * @ApiRoute (/api/music/{id}/result)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="任务ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "task_id": 123,
     *     "audio_url": "https://aiapi.tiptop.cn/music/generated/123.mp3",
     *     "waveform_url": "https://aiapi.tiptop.cn/music/waveforms/123.png",
     *     "metadata": {
     *       "duration": 60,
     *       "format": "mp3",
     *       "bitrate": "320kbps",
     *       "sample_rate": "44100Hz",
     *       "file_size": "3.2MB",
     *       "genre": "pop",
     *       "mood": "happy"
     *     },
     *     "download_info": {
     *       "direct_url": "https://aiapi.tiptop.cn/music/generated/123.mp3",
     *       "expires_at": "2024-01-08 12:00:00"
     *     }
     *   }
     * })
     */
    public function getResult(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->musicService->getMusicResult($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (批量音乐生成)
     * @ApiSummary (批量生成多首音乐)
     * @ApiMethod (POST)
     * @ApiRoute (/api/batch/music/generate)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="prompts", type="array", required=true, description="音乐提示词数组")
     * @ApiParams (name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams (name="common_params", type="object", required=false, description="通用参数")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "批量音乐生成任务创建成功",
     *   "data": {
     *     "batch_id": "batch_123",
     *     "task_ids": [123, 124, 125],
     *     "total_count": 3,
     *     "estimated_cost": "0.6000"
     *   }
     * })
     */
    public function batchGenerate(Request $request)
    {
        $rules = [
            'prompts' => 'required|array|min:1|max:10',
            'prompts.*' => 'required|string|min:5|max:1000',
            'project_id' => 'sometimes|integer|exists:projects,id',
            'common_params' => 'sometimes|array'
        ];

        $messages = [
            'prompts.required' => '提示词数组不能为空',
            'prompts.min' => '至少需要1个提示词',
            'prompts.max' => '最多支持10个提示词',
            'prompts.*.required' => '提示词不能为空',
            'prompts.*.min' => '提示词至少5个字符',
            'prompts.*.max' => '提示词不能超过1000个字符',
            'project_id.exists' => '项目不存在'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $result = $this->musicService->batchGenerateMusic(
            $user->id,
            $request->prompts,
            $request->project_id,
            $request->get('common_params', [])
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
