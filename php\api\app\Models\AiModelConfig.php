<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * AI模型配置模型
 * 
 * @property int $id
 * @property string $platform
 * @property string $model_name
 * @property string $model_type
 * @property string $api_endpoint
 * @property array $config_params
 * @property array $capabilities
 * @property bool $is_active
 * @property bool $is_default
 * @property int $priority
 * @property float $cost_per_request
 * @property int $max_tokens
 * @property int $timeout_seconds
 * @property array $rate_limits
 * @property array $performance_metrics
 * @property \Carbon\Carbon $last_health_check
 * @property string $health_status
 * @property string $health_message
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class AiModelConfig extends Model
{
    use SoftDeletes;
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'ai_model_configs';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'platform',
        'model_name',
        'model_type',
        'api_endpoint',
        'config_params',
        'capabilities',
        'is_active',
        'is_default',
        'priority',
        'cost_per_request',
        'max_tokens',
        'timeout_seconds',
        'rate_limits',
        'performance_metrics',
        'last_health_check',
        'health_status',
        'health_message'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'config_params' => 'array',
        'capabilities' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'priority' => 'integer',
        'cost_per_request' => 'decimal:4',
        'max_tokens' => 'integer',
        'timeout_seconds' => 'integer',
        'rate_limits' => 'array',
        'performance_metrics' => 'array',
        'last_health_check' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'is_active' => true,
        'is_default' => false,
        'priority' => 0,
        'cost_per_request' => 0,
        'timeout_seconds' => 30,
        'health_status' => 'unknown'
    ];

    /**
     * 平台常量
     */
    const PLATFORM_DEEPSEEK = 'deepseek';
    const PLATFORM_LIBLIB = 'liblib';
    const PLATFORM_KLING = 'kling';
    const PLATFORM_MINIMAX = 'minimax';
    const PLATFORM_VOLCENGINE = 'volcengine';

    /**
     * 模型类型常量
     */
    const TYPE_TEXT_GENERATION = 'text_generation';
    const TYPE_IMAGE_GENERATION = 'image_generation';
    const TYPE_VIDEO_GENERATION = 'video_generation';
    const TYPE_VOICE_SYNTHESIS = 'voice_synthesis';
    const TYPE_MUSIC_GENERATION = 'music_generation';
    const TYPE_SOUND_GENERATION = 'sound_generation';

    /**
     * 健康状态常量
     */
    const HEALTH_HEALTHY = 'healthy';
    const HEALTH_DEGRADED = 'degraded';
    const HEALTH_UNHEALTHY = 'unhealthy';
    const HEALTH_UNKNOWN = 'unknown';

    /**
     * 关联AI生成任务
     */
    public function generationTasks(): HasMany
    {
        return $this->hasMany(AiGenerationTask::class, 'model_config_id');
    }

    /**
     * 获取配置参数
     */
    public function getConfigParam(string $key, $default = null)
    {
        return data_get($this->config_params, $key, $default);
    }

    /**
     * 设置配置参数
     */
    public function setConfigParam(string $key, $value): void
    {
        $params = $this->config_params ?? [];
        data_set($params, $key, $value);
        $this->config_params = $params;
    }

    /**
     * 检查是否支持某种能力
     */
    public function hasCapability(string $capability): bool
    {
        return in_array($capability, $this->capabilities ?? []);
    }

    /**
     * 添加能力
     */
    public function addCapability(string $capability): void
    {
        $capabilities = $this->capabilities ?? [];
        if (!in_array($capability, $capabilities)) {
            $capabilities[] = $capability;
            $this->capabilities = $capabilities;
        }
    }

    /**
     * 更新健康状态
     */
    public function updateHealthStatus(string $status, ?string $message = null): void
    {
        $this->health_status = $status;
        $this->health_message = $message;
        $this->last_health_check = Carbon::now();
        $this->save();
    }

    /**
     * 检查是否健康
     */
    public function isHealthy(): bool
    {
        return $this->health_status === self::HEALTH_HEALTHY;
    }

    /**
     * 获取性能指标
     */
    public function getPerformanceMetric(string $key, $default = null)
    {
        return data_get($this->performance_metrics, $key, $default);
    }

    /**
     * 设置性能指标
     */
    public function setPerformanceMetric(string $key, $value): void
    {
        $metrics = $this->performance_metrics ?? [];
        data_set($metrics, $key, $value);
        $this->performance_metrics = $metrics;
    }

    /**
     * 作用域：活跃的模型
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按平台筛选
     */
    public function scopeByPlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * 作用域：按模型类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('model_type', $type);
    }

    /**
     * 作用域：默认模型
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * 作用域：健康的模型
     */
    public function scopeHealthy($query)
    {
        return $query->where('health_status', self::HEALTH_HEALTHY);
    }

    /**
     * 作用域：按优先级排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('priority', 'desc')->orderBy('cost_per_request', 'asc');
    }

    /**
     * 作用域：可用的模型（活跃且健康）
     */
    public function scopeAvailable($query)
    {
        return $query->active()->healthy();
    }
}
