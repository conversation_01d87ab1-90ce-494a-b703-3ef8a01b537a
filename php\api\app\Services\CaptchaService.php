<?php

namespace App\Services;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CaptchaService
{
    /**
     * 生成验证码
     */
    public function generateCaptcha($type, $phone = null, $userId = null)
    {
        // 生成验证码
        $captchaId = 'cap_' . Str::random(16);
        $code = $this->generateCode($type);
        $expiresAt = now()->addMinutes(5);

        // 存储验证码
        $captchaData = [
            'code' => $code,
            'type' => $type,
            'user_id' => $userId,
            'phone' => $phone,
            'attempts' => 0,
            'created_at' => now(),
            'expires_at' => $expiresAt
        ];

        Cache::put("captcha:{$captchaId}", $captchaData, 300); // 5分钟过期

        // 根据类型处理
        $response = [
            'captcha_id' => $captchaId, 
            'expires_at' => $expiresAt->format('Y-m-d H:i:s'), 
            'type' => $type
        ];

        switch ($type) {
            case 'image':
                $response['image_url'] = $this->generateImageCaptcha($code);
                break;
            case 'sms':
                $this->sendSMSCaptcha($phone, $code);
                $response['phone'] = substr($phone, 0, 3) . '****' . substr($phone, -4);
                break;
            case 'email':
                $email = auth()->user()->email ?? '';
                $this->sendEmailCaptcha($email, $code);
                $response['email'] = $this->maskEmail($email);
                break;
        }

        // 记录频率限制
        $this->recordRateLimit($userId, $type);

        return $response;
    }

    /**
     * 验证验证码
     */
    public function verifyCaptcha($captchaId, $inputCode)
    {
        // 获取验证码数据
        $captchaData = Cache::get("captcha:{$captchaId}");

        if (!$captchaData) {
            return [
                'success' => false,
                'message' => '验证码不存在或已过期',
                'code' => 1051
            ];
        }

        // 检查尝试次数
        if ($captchaData['attempts'] >= 3) {
            Cache::forget("captcha:{$captchaId}");
            return [
                'success' => false,
                'message' => '验证码尝试次数过多，请重新获取',
                'code' => 1050
            ];
        }

        // 验证验证码
        if (strtolower($inputCode) !== strtolower($captchaData['code'])) {
            $captchaData['attempts']++;
            Cache::put("captcha:{$captchaId}", $captchaData, 300);
            return [
                'success' => false,
                'message' => '验证码错误',
                'code' => 1050
            ];
        }

        // 验证成功，删除验证码
        Cache::forget("captcha:{$captchaId}");

        // 记录验证成功
        $this->logCaptchaVerification($captchaId, $captchaData['user_id'], true);

        return [
            'success' => true,
            'message' => '验证码验证成功'
        ];
    }

    /**
     * 刷新验证码
     */
    public function refreshCaptcha($oldCaptchaId)
    {
        // 获取原验证码数据
        $oldCaptchaData = Cache::get("captcha:{$oldCaptchaId}");

        if (!$oldCaptchaData) {
            return [
                'success' => false,
                'message' => '验证码不存在',
                'code' => 404
            ];
        }

        // 检查是否为图像验证码
        if ($oldCaptchaData['type'] !== 'image') {
            return [
                'success' => false,
                'message' => '只有图像验证码支持刷新',
                'code' => 422
            ];
        }

        // 删除旧验证码
        Cache::forget("captcha:{$oldCaptchaId}");

        // 生成新验证码
        $newCaptchaId = 'cap_' . Str::random(16);
        $newCode = $this->generateCode('image');
        $expiresAt = now()->addMinutes(5);

        $newCaptchaData = [
            'code' => $newCode,
            'type' => 'image',
            'user_id' => $oldCaptchaData['user_id'],
            'attempts' => 0,
            'created_at' => now(),
            'expires_at' => $expiresAt
        ];

        Cache::put("captcha:{$newCaptchaId}", $newCaptchaData, 300);

        return [
            'success' => true,
            'data' => [
                'captcha_id' => $newCaptchaId,
                'image_url' => $this->generateImageCaptcha($newCode),
                'expires_at' => $expiresAt->format('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 检查频率限制
     */
    public function checkRateLimit($userId, $type)
    {
        $key = "captcha_rate_limit:{$type}:{$userId}";
        
        // 每分钟最多3次，每小时最多10次
        $minuteLimit = $type === 'sms' ? 1 : 3;
        $hourLimit = $type === 'sms' ? 5 : 10;
        
        $minuteKey = $key . ':minute:' . now()->format('Y-m-d-H-i');
        $hourKey = $key . ':hour:' . now()->format('Y-m-d-H');
        
        $minuteCount = Redis::get($minuteKey) ?: 0;
        $hourCount = Redis::get($hourKey) ?: 0;
        
        return $minuteCount < $minuteLimit && $hourCount < $hourLimit;
    }

    /**
     * 生成验证码
     */
    private function generateCode($type)
    {
        switch ($type) {
            case 'image':
                return strtoupper(Str::random(4)); // 4位字母数字组合
            case 'sms':
            case 'email':
                return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT); // 6位数字
            default:
                return strtoupper(Str::random(4));
        }
    }

    /**
     * 生成图像验证码
     */
    private function generateImageCaptcha($code)
    {
        // 这里应该调用图像验证码生成库
        // 返回base64编码的图像数据
        return 'data:image/png;base64,' . base64_encode('mock_image_data_' . $code);
    }

    /**
     * 发送短信验证码
     */
    private function sendSMSCaptcha($phone, $code)
    {
        // 这里应该调用短信服务
        Log::info("SMS验证码发送", ['phone' => $phone, 'code' => $code]);
    }

    /**
     * 发送邮件验证码
     */
    private function sendEmailCaptcha($email, $code)
    {
        // 这里应该调用邮件服务
        Log::info("邮件验证码发送", ['email' => $email, 'code' => $code]);
    }

    /**
     * 记录频率限制
     */
    private function recordRateLimit($userId, $type)
    {
        $key = "captcha_rate_limit:{$type}:{$userId}";
        $minuteKey = $key . ':minute:' . now()->format('Y-m-d-H-i');
        $hourKey = $key . ':hour:' . now()->format('Y-m-d-H');
        
        Redis::incr($minuteKey);
        Redis::expire($minuteKey, 60);
        
        Redis::incr($hourKey);
        Redis::expire($hourKey, 3600);
    }

    /**
     * 记录验证码验证日志
     */
    private function logCaptchaVerification($captchaId, $userId, $success)
    {
        Log::info('验证码验证', [
            'captcha_id' => $captchaId,
            'user_id' => $userId,
            'success' => $success,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * 邮箱掩码
     */
    private function maskEmail($email)
    {
        if (empty($email)) return '';
        
        $parts = explode('@', $email);
        $username = $parts[0];
        $domain = $parts[1] ?? '';
        
        if (strlen($username) <= 3) {
            return $username[0] . '***@' . $domain;
        }
        
        return substr($username, 0, 2) . '***' . substr($username, -1) . '@' . $domain;
    }
}
