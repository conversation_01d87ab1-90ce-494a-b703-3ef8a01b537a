<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 用户模型偏好模型
 * 
 * @property int $id
 * @property int $user_id
 * @property string $business_type
 * @property string $preferred_platform
 * @property array $platform_priorities
 * @property array $selection_criteria
 * @property bool $auto_fallback
 * @property bool $cost_optimization
 * @property array $custom_config
 * @property int $usage_count
 * @property \Carbon\Carbon $last_used_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 */
class UserModelPreference extends Model
{
    use SoftDeletes;

    /**
     * 表名
     */
    protected $table = 'user_model_preferences';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'business_type',
        'preferred_platform',
        'platform_priorities',
        'selection_criteria',
        'auto_fallback',
        'cost_optimization',
        'custom_config',
        'usage_count',
        'last_used_at'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'platform_priorities' => 'array',
        'selection_criteria' => 'array',
        'custom_config' => 'array',
        'auto_fallback' => 'boolean',
        'cost_optimization' => 'boolean',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取平台优先级
     */
    public function getPlatformPriority(string $platform): int
    {
        return $this->platform_priorities[$platform] ?? 50; // 默认优先级50
    }

    /**
     * 设置平台优先级
     */
    public function setPlatformPriority(string $platform, int $priority): void
    {
        $priorities = $this->platform_priorities ?? [];
        $priorities[$platform] = max(0, min(100, $priority)); // 限制在0-100之间
        $this->platform_priorities = $priorities;
    }

    /**
     * 获取选择标准权重
     */
    public function getSelectionCriteriaWeight(string $criteria): float
    {
        return $this->selection_criteria[$criteria] ?? 0.0;
    }

    /**
     * 设置选择标准权重
     */
    public function setSelectionCriteriaWeight(string $criteria, float $weight): void
    {
        $criteriaWeights = $this->selection_criteria ?? [];
        $criteriaWeights[$criteria] = max(0.0, min(1.0, $weight)); // 限制在0-1之间
        $this->selection_criteria = $criteriaWeights;
    }

    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        $this->last_used_at = now();
        $this->save();
    }

    /**
     * 获取自定义配置
     */
    public function getCustomConfig(string $key, $default = null)
    {
        return $this->custom_config[$key] ?? $default;
    }

    /**
     * 设置自定义配置
     */
    public function setCustomConfig(string $key, $value): void
    {
        $config = $this->custom_config ?? [];
        $config[$key] = $value;
        $this->custom_config = $config;
    }

    /**
     * 业务类型常量
     */
    const BUSINESS_TYPE_IMAGE = 'image';
    const BUSINESS_TYPE_VIDEO = 'video';
    const BUSINESS_TYPE_STORY = 'story';
    const BUSINESS_TYPE_CHARACTER = 'character';
    const BUSINESS_TYPE_STYLE = 'style';
    const BUSINESS_TYPE_VOICE = 'voice';
    const BUSINESS_TYPE_SOUND = 'sound';
    const BUSINESS_TYPE_MUSIC = 'music';

    /**
     * 获取所有业务类型
     */
    public static function getBusinessTypes(): array
    {
        return [
            self::BUSINESS_TYPE_IMAGE,
            self::BUSINESS_TYPE_VIDEO,
            self::BUSINESS_TYPE_STORY,
            self::BUSINESS_TYPE_CHARACTER,
            self::BUSINESS_TYPE_STYLE,
            self::BUSINESS_TYPE_VOICE,
            self::BUSINESS_TYPE_SOUND,
            self::BUSINESS_TYPE_MUSIC
        ];
    }

    /**
     * 选择标准常量
     */
    const CRITERIA_PERFORMANCE = 'performance';
    const CRITERIA_COST = 'cost';
    const CRITERIA_QUALITY = 'quality';
    const CRITERIA_RELIABILITY = 'reliability';
    const CRITERIA_SPEED = 'speed';

    /**
     * 获取默认选择标准
     */
    public static function getDefaultSelectionCriteria(): array
    {
        return [
            self::CRITERIA_PERFORMANCE => 0.3,
            self::CRITERIA_COST => 0.2,
            self::CRITERIA_QUALITY => 0.2,
            self::CRITERIA_RELIABILITY => 0.2,
            self::CRITERIA_SPEED => 0.1
        ];
    }

    /**
     * 创建默认偏好设置
     */
    public static function createDefault(int $userId, string $businessType, string $preferredPlatform): self
    {
        return self::create([
            'user_id' => $userId,
            'business_type' => $businessType,
            'preferred_platform' => $preferredPlatform,
            'platform_priorities' => [$preferredPlatform => 80], // 首选平台给80分
            'selection_criteria' => self::getDefaultSelectionCriteria(),
            'auto_fallback' => true,
            'cost_optimization' => false,
            'custom_config' => [],
            'usage_count' => 0
        ]);
    }

    /**
     * 范围查询：按业务类型
     */
    public function scopeByBusinessType($query, string $businessType)
    {
        return $query->where('business_type', $businessType);
    }

    /**
     * 范围查询：按用户ID
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 范围查询：启用自动降级
     */
    public function scopeAutoFallbackEnabled($query)
    {
        return $query->where('auto_fallback', true);
    }

    /**
     * 范围查询：启用成本优化
     */
    public function scopeCostOptimizationEnabled($query)
    {
        return $query->where('cost_optimization', true);
    }
}
