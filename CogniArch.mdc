# CogniArch 架构师战略蓝图文档

## 当前任务状态
- 任务类型：文档对比分析
- 执行时间：2025-07-29
- 任务描述：检测 apitest-final.mdc 中多出的API接口是合理的还是重复的

## 战略蓝图

### 任务分析
用户要求检测 apitest-final.mdc 相比 apitest-url.mdc 多出的API接口是否合理或重复。

### 执行结果分析

#### 接口数量统计
- **apitest-url.mdc**: 283个API接口
- **apitest-final.mdc**: 284个API接口
- **差异**: apitest-final.mdc 多出 1个API接口

#### 多出接口识别
通过详细对比分析，发现多出的接口为：
- **接口位置**: 第17676行
- **接口定义**: `步骤5: 35.5 文件版本管理 GET /api/files/{id}/versions`

#### 对比验证
在 apitest-url.mdc 中，对应位置的接口为：
- **接口定义**: `步骤5: 35.5 文件下载 GET /api/files/{id}/download`

### 合理性判断

**结论：多出的接口是不合理的，属于接口替换错误**

**详细分析**：
1. **原始接口**: `GET /api/files/{id}/download` (文件下载)
2. **替换接口**: `GET /api/files/{id}/versions` (文件版本管理)
3. **问题性质**: 这不是新增接口，而是错误地将原有的文件下载接口替换为了文件版本管理接口

**影响评估**：
- 导致文件下载功能缺失
- 引入了未在原始规划中的文件版本管理功能
- 破坏了API接口的一致性和完整性

### 修正建议
1. 将 apitest-final.mdc 第17676行的接口恢复为原始的文件下载接口
2. 如需添加文件版本管理功能，应作为新增接口处理，而非替换现有接口

## 应用规则
- 基于 Triumvirate Protocol（三体协议）开发模式
- 遵循 @.cursor/rules/ 文件夹内的规范要求

## 应用模型
- Claude Sonnet 4 by Anthropic

---

## 新任务：VersionController 接口缺失分析

### 任务背景
用户发现 apitest-final.mdc 中缺失 apitest-code.mdc 中定义的 `/api/resources/{id}/versions` 接口。

### 问题分析

#### 缺失接口识别
通过对比分析发现，apitest-code.mdc 中定义的 **VersionController (6个接口)** 在 apitest-final.mdc 中完全缺失：

1. **11.1** 创建资源版本 `POST /api/resources/{id}/versions`
2. **11.2** 获取版本历史 `GET /api/resources/{id}/versions`
3. **11.3** 获取版本详情 `GET /api/versions/{id}`
4. **11.4** 设置当前版本 `PUT /api/versions/{id}/set-current`
5. **11.5** 删除版本 `DELETE /api/versions/{id}`
6. **11.6** 版本比较 `GET /api/versions/compare`

#### 根本原因分析
1. **规划遗漏**：apitest-url.mdc 虽然提到了 "VersionController.php (6个接口)：版本控制"，但未在具体的测试阶段中列出这些接口
2. **文档不同步**：apitest-code.mdc 和 apitest-final.mdc 之间存在同步缺失
3. **功能完整性缺陷**：版本控制是资源管理的重要组成部分，缺失会影响系统完整性

### 战略规划方案

#### 方案A：补充到第七阶段资源管理系统
**位置**：第七阶段 7.16 资源管理系统之后
**理由**：版本控制是资源管理的核心功能，应与资源管理接口紧密关联

#### 方案B：创建独立的版本控制模块
**位置**：第七阶段新增 7.29 版本控制系统
**理由**：版本控制功能独立性强，可作为独立模块管理

### 推荐方案：方案A

**具体规划**：
- **位置**：在 apitest-final.mdc 第七阶段 7.16 资源管理系统（9个接口）之后
- **新增内容**：7.17 版本控制系统（6个接口）
- **测试顺序**：按照 apitest-code.mdc 中定义的 11.1-11.6 顺序

### 实施计划
1. **第一步**：在 apitest-final.mdc 中定位到资源管理系统结束位置
2. **第二步**：按照现有格式添加版本控制系统的6个接口
3. **第三步**：为每个接口补充完整的请求参数和响应示例
4. **第四步**：更新接口总数统计

### 影响评估
- **接口数量变化**：从284个增加到290个
- **功能完整性**：显著提升资源管理功能的完整性
- **测试覆盖率**：提高版本控制功能的测试覆盖

## 接管任务
@CogniDev 请接管任务，按照战略规划在 apitest-final.mdc 中补充 VersionController 的6个接口