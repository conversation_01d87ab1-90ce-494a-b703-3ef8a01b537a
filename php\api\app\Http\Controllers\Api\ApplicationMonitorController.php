<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\MonitorService;
use Illuminate\Http\Request;

/**
 * 应用监控控制器（重命名避免与SystemMonitorController冲突）
 * 处理应用级监控、告警管理、实时数据等
 */
class ApplicationMonitorController extends Controller
{
    protected $monitorService;

    public function __construct(MonitorService $monitorService)
    {
        $this->monitorService = $monitorService;
    }

    /**
     * @ApiTitle(系统健康检查)
     * @ApiSummary(检查系统各组件的健康状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/monitor/health)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "status": "healthy",
     *     "timestamp": "2024-01-01 12:00:00",
     *     "components": {
     *       "database": {
     *         "status": "healthy",
     *         "response_time": 15,
     *         "connections": 5
     *       },
     *       "redis": {
     *         "status": "healthy",
     *         "response_time": 2,
     *         "memory_usage": "45%"
     *       },
     *       "ai_services": {
     *         "status": "healthy",
     *         "available_platforms": 4,
     *         "total_platforms": 5
     *       },
     *       "storage": {
     *         "status": "healthy",
     *         "disk_usage": "65%",
     *         "available_space": "500GB"
     *       }
     *     },
     *     "overall_score": 95
     *   }
     * })
     */
    public function health(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        // 检查管理员权限
        if (!$user->is_admin) {
            return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看系统健康状态');
        }

        $result = $this->monitorService->getSystemHealth();

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(系统性能指标)
     * @ApiSummary(获取系统性能监控指标)
     * @ApiMethod(GET)
     * @ApiRoute(/api/monitor/metrics)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="period", type="string", required=false, description="时间周期：1h,6h,24h,7d,30d")
     * @ApiParams(name="metrics", type="string", required=false, description="指标类型：cpu,memory,disk,network,api")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "period": "24h",
     *     "timestamp": "2024-01-01 12:00:00",
     *     "system": {
     *       "cpu_usage": {
     *         "current": 45.2,
     *         "average": 38.5,
     *         "peak": 78.9,
     *         "trend": "stable"
     *       },
     *       "memory_usage": {
     *         "current": 62.1,
     *         "average": 58.3,
     *         "peak": 85.4,
     *         "trend": "increasing"
     *       },
     *       "disk_usage": {
     *         "current": 65.0,
     *         "available": "500GB",
     *         "trend": "stable"
     *       }
     *     },
     *     "api": {
     *       "total_requests": 15420,
     *       "success_rate": 99.2,
     *       "average_response_time": 185,
     *       "error_rate": 0.8,
     *       "top_endpoints": [
     *         {
     *           "endpoint": "/api/images/generate",
     *           "requests": 3250,
     *           "avg_time": 2500
     *         }
     *       ]
     *     },
     *     "ai_services": {
     *       "total_calls": 8520,
     *       "success_rate": 96.5,
     *       "average_time": 3200,
     *       "by_platform": {
     *         "liblib": {"calls": 3200, "success_rate": 98.1},
     *         "deepseek": {"calls": 2100, "success_rate": 97.8},
     *         "kling": {"calls": 1800, "success_rate": 94.2},
     *         "minimax": {"calls": 1420, "success_rate": 95.8}
     *       }
     *     }
     *   }
     * })
     */
    public function metrics(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        // 检查管理员权限
        if (!$user->is_admin) {
            return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看系统指标');
        }

        $rules = [
            'period' => 'sometimes|string|in:1h,6h,24h,7d,30d',
            'metrics' => 'sometimes|string|in:cpu,memory,disk,network,api,ai'
        ];

        $this->validateData($request->all(), $rules);

        $filters = [
            'period' => $request->get('period', '24h'),
            'metrics' => $request->get('metrics')
        ];

        $result = $this->monitorService->getSystemMetrics($filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(实时监控数据)
     * @ApiSummary(获取实时系统监控数据)
     * @ApiMethod(GET)
     * @ApiRoute(/api/monitor/realtime)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "timestamp": "2024-01-01 12:00:00",
     *     "active_users": 156,
     *     "active_sessions": 89,
     *     "current_load": {
     *       "cpu": 45.2,
     *       "memory": 62.1,
     *       "disk_io": 23.5,
     *       "network_io": 12.8
     *     },
     *     "api_stats": {
     *       "requests_per_minute": 85,
     *       "errors_per_minute": 2,
     *       "avg_response_time": 185
     *     },
     *     "ai_services": {
     *       "active_tasks": 12,
     *       "queue_length": 5,
     *       "processing_time": 2800
     *     },
     *     "alerts": [
     *       {
     *         "level": "warning",
     *         "message": "CPU使用率较高",
     *         "value": 78.9,
     *         "threshold": 75.0
     *       }
     *     ]
     *   }
     * })
     */
    public function realtime(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        // 检查管理员权限
        if (!$user->is_admin) {
            return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看实时监控');
        }

        $result = $this->monitorService->getRealtimeData();

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(系统告警列表)
     * @ApiSummary(获取系统告警信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/monitor/alerts)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="level", type="string", required=false, description="告警级别：info,warning,error,critical")
     * @ApiParams(name="status", type="string", required=false, description="告警状态：active,resolved,acknowledged")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "alerts": [
     *       {
     *         "id": 1,
     *         "level": "warning",
     *         "title": "CPU使用率过高",
     *         "message": "系统CPU使用率达到78.9%，超过阈值75%",
     *         "component": "system",
     *         "status": "active",
     *         "value": 78.9,
     *         "threshold": 75.0,
     *         "created_at": "2024-01-01 12:00:00",
     *         "acknowledged_at": null,
     *         "resolved_at": null
     *       }
     *     ],
     *     "summary": {
     *       "total": 15,
     *       "active": 3,
     *       "critical": 0,
     *       "warning": 2,
     *       "info": 1
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 15,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function alerts(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        // 检查管理员权限
        if (!$user->is_admin) {
            return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看系统告警');
        }

        $rules = [
            'level' => 'sometimes|string|in:info,warning,error,critical',
            'status' => 'sometimes|string|in:active,resolved,acknowledged',
            'page' => 'sometimes|integer|min:1'
        ];

        $this->validateData($request->all(), $rules);

        $filters = [
            'level' => $request->get('level'),
            'status' => $request->get('status'),
            'page' => $request->get('page', 1),
            'per_page' => 20
        ];

        $result = $this->monitorService->getAlerts($filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(确认告警)
     * @ApiSummary(确认处理指定的系统告警)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/monitor/alerts/{id}/acknowledge)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="告警ID")
     * @ApiParams(name="note", type="string", required=false, description="处理备注")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "告警确认成功",
     *   "data": {
     *     "alert_id": 1,
     *     "status": "acknowledged",
     *     "acknowledged_by": "admin",
     *     "acknowledged_at": "2024-01-01 12:00:00",
     *     "note": "已知问题，正在处理中"
     *   }
     * })
     */
    public function acknowledgeAlert($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        // 检查管理员权限
        if (!$user->is_admin) {
            return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可确认告警');
        }

        $rules = [
            'note' => 'sometimes|string|max:500'
        ];

        $this->validateData($request->all(), $rules);

        $note = $request->get('note', '', []);

        $result = $this->monitorService->acknowledgeAlert($id, $user->id, $note);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(解决告警)
     * @ApiSummary(标记告警为已解决)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/monitor/alerts/{id}/resolve)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="告警ID")
     * @ApiParams(name="solution", type="string", required=false, description="解决方案描述")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "告警解决成功",
     *   "data": {
     *     "alert_id": 1,
     *     "status": "resolved",
     *     "resolved_by": "admin",
     *     "resolved_at": "2024-01-01 12:00:00",
     *     "solution": "优化了数据库查询，CPU使用率已降低"
     *   }
     * })
     */
    public function resolveAlert($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        // 检查管理员权限
        if (!$user->is_admin) {
            return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可解决告警');
        }

        $rules = [
            'solution' => 'sometimes|string|max:1000'
        ];

        $this->validateData($request->all(), $rules);

        $solution = $request->get('solution', '', []);

        $result = $this->monitorService->resolveAlert($id, $user->id, $solution);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
