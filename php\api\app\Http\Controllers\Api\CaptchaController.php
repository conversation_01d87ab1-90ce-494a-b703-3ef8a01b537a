<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CaptchaService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;

/**
 * @ApiTitle(验证码安全控制器)
 * @ApiDescription("提供验证码生成、验证、刷新等安全功能")
 * @ApiPrefix("/api/captcha")
 * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
 */
class CaptchaController extends Controller
{
    protected $captchaService;

    public function __construct(CaptchaService $captchaService)
    {
        $this->captchaService = $captchaService;
    }

    /**
     * @ApiTitle(生成验证码)
     * @ApiMethod(GET)
     * @ApiRoute(/api/captcha/generate)
     * @ApiParams(name="type", type="string", required=true, description="验证码类型：image|sms|email")
     * @ApiParams(name="phone", type="string", required=false, description="手机号（SMS类型必填）")
     * @ApiReturn({"code": 200, "message": "验证码生成成功", "data": {"captcha_id": "cap_123456", "image_url": "data:image/png;base64,xxx", "expires_at": "2023-01-01 12:05:00", "type": "image"}})
     */
    public function generate(Request $request)
    {
        $this->validate($request, [
            'type' => 'required|in:image,sms,email',
            'phone' => 'required_if:type,sms|regex:/^1[3-9]\d{9}$/'
        ]);

        try {
            $type = $request->input('type');
            $phone = $request->input('phone');
            $userId = auth()->id();

            // 检查频率限制
            if (!$this->captchaService->checkRateLimit($userId, $type)) {
                return $this->errorResponse('验证码发送频率限制，请稍后再试', 1052);
            }

            // 生成验证码
            $result = $this->captchaService->generateCaptcha($type, $phone, $userId);

            return $this->successResponse($result);

        } catch (\Exception $e) {
            return $this->errorResponse('验证码生成失败', 500);
        }
    }

    /**
     * @ApiTitle(验证验证码)
     * @ApiMethod(POST)
     * @ApiRoute(/api/captcha/verify)
     * @ApiParams(name="captcha_id", type="string", required=true, description="验证码ID")
     * @ApiParams(name="code", type="string", required=true, description="验证码")
     * @ApiReturn({"code": 200, "message": "验证码验证成功", "data": {"verified": true, "captcha_id": "cap_123456", "verified_at": "2023-01-01 12:03:00"}})
     */
    public function verify(Request $request)
    {
        $this->validate($request, [
            'captcha_id' => 'required|string',
            'code' => 'required|string'
        ]);

        try {
            $captchaId = $request->input('captcha_id');
            $inputCode = $request->input('code');

            $result = $this->captchaService->verifyCaptcha($captchaId, $inputCode);

            if ($result['success']) {
                return $this->successResponse([
                    'verified' => true,
                    'captcha_id' => $captchaId,
                    'verified_at' => now()->format('Y-m-d H:i:s')
                ]);
            } else {
                return $this->errorResponse($result['message'], $result['code']);
            }

        } catch (\Exception $e) {
            return $this->errorResponse('验证码验证失败', 500);
        }
    }

    /**
     * @ApiTitle(刷新验证码)
     * @ApiMethod(POST)
     * @ApiRoute(/api/captcha/refresh)
     * @ApiParams(name="captcha_id", type="string", required=true, description="验证码ID")
     * @ApiReturn({"code": 200, "message": "验证码刷新成功", "data": {"captcha_id": "cap_789012", "image_url": "data:image/png;base64,yyy", "expires_at": "2023-01-01 12:10:00"}})
     */
    public function refresh(Request $request)
    {
        $this->validate($request, [
            'captcha_id' => 'required|string'
        ]);

        try {
            $oldCaptchaId = $request->input('captcha_id');

            $result = $this->captchaService->refreshCaptcha($oldCaptchaId);

            if ($result['success']) {
                return $this->successResponse($result['data']);
            } else {
                return $this->errorResponse($result['message'], $result['code']);
            }

        } catch (\Exception $e) {
            return $this->errorResponse('验证码刷新失败', 500);
        }
    }
}
