<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\DataExportService;
use Illuminate\Http\Request;

/**
 * 数据导出与任务管理
 */
class DataExportController extends Controller
{
    protected $exportService;

    public function __construct(DataExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * @ApiTitle (创建数据导出)
     * @ApiSummary (创建数据导出任务)
     * @ApiMethod (POST)
     * @ApiRoute (/api/exports/create)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="export_type", type="string", required=true, description="导出类型")
     * @ApiParams (name="export_format", type="string", required=true, description="导出格式")
     * @ApiParams (name="export_params", type="object", required=false, description="导出参数")
     * @ApiParams (name="export_filters", type="object", required=false, description="导出筛选条件")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="导出任务信息")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "导出任务创建成功",
     *   "data": {
     *     "export_id": 123,
     *     "export_type": "user_data",
     *     "export_format": "csv",
     *     "status": "pending",
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function createExport(Request $request)
    {
        $rules = [
            'export_type' => 'required|string|in:user_data,projects,ai_tasks,characters,points_history,files',
            'export_format' => 'required|string|in:csv,excel,json,pdf,zip',
            'export_params' => 'sometimes|array',
            'export_filters' => 'sometimes|array'
        ];

        $messages = [
            'export_type.required' => '导出类型不能为空',
            'export_type.in' => '导出类型无效',
            'export_format.required' => '导出格式不能为空',
            'export_format.in' => '导出格式无效'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->exportService->createExport(
            $user->id,
            $request->export_type,
            $request->export_format,
            $request->export_params ?? [],
            $request->export_filters ?? []
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (导出任务列表)
     * @ApiSummary (获取用户的导出任务列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/exports/list)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="export_type", type="string", required=false, description="导出类型筛选")
     * @ApiParams (name="status", type="string", required=false, description="状态筛选")
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="导出任务列表")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "exports": [
     *       {
     *         "id": 123,
     *         "export_type": "user_data",
     *         "export_format": "csv",
     *         "status": "completed",
     *         "file_size": 1024000,
     *         "human_file_size": "1.00 MB",
     *         "record_count": 100,
     *         "progress_percentage": 100,
     *         "created_at": "2024-01-01 12:00:00",
     *         "completed_at": "2024-01-01 12:01:00",
     *         "expires_at": "2024-01-08 12:01:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 10,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function getExports(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $filters = [
            'export_type' => $request->get('export_type'),
            'status' => $request->get('status')
        ];

        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 20), 100);

        $result = $this->exportService->getUserExports($user->id, $filters, $page, $perPage);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (导出任务状态)
     * @ApiSummary (获取导出任务状态和进度)
     * @ApiMethod (GET)
     * @ApiRoute (/api/exports/{id}/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="导出任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务状态")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "export_type": "user_data",
     *     "status": "processing",
     *     "progress": {
     *       "processed": 50,
     *       "total": 100,
     *       "percentage": 50.0,
     *       "message": "正在导出用户数据..."
     *     },
     *     "created_at": "2024-01-01 12:00:00",
     *     "started_at": "2024-01-01 12:00:05"
     *   }
     * })
     */
    public function getExportStatus(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->exportService->getExportStatus($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (下载导出文件)
     * @ApiSummary (下载已完成的导出文件)
     * @ApiMethod (GET)
     * @ApiRoute (/api/exports/{id}/download)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="导出任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="下载信息")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "download_url": "https://example.com/storage/exports/export_20240101_123456.csv",
     *     "filename": "user_data_export.csv",
     *     "file_size": 1024000,
     *     "expires_at": "2024-01-08 12:01:00"
     *   }
     * })
     */
    public function downloadExport(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->exportService->downloadExport($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
