<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AiModelConfig;

class AiModelConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 🔧 修正：基于正确的业务平台映射配置创建种子数据
        $models = [
            // DeepSeek 模型
            [
                'platform' => 'deepseek',
                'model_name' => 'deepseek-chat',
                'model_type' => 'text_generation',
                'api_endpoint' => 'https://api.deepseek.com/v1/chat/completions',
                'config_params' => [
                    'api_key' => env('DEEPSEEK_API_KEY', ''),
                    'base_url' => 'https://api.deepseek.com',
                    'version' => 'v1'
                ],
                'capabilities' => ['chat', 'completion', 'streaming'],
                'is_active' => true,
                'is_default' => true,
                'priority' => 100,
                'cost_per_request' => 0.0014,
                'max_tokens' => 4000,
                'timeout_seconds' => 30,
                'rate_limits' => [
                    'requests_per_minute' => 60,
                    'tokens_per_minute' => 100000
                ],
                'health_status' => 'healthy'
            ],
            [
                'platform' => 'deepseek',
                'model_name' => 'deepseek-coder',
                'model_type' => 'text_generation',
                'api_endpoint' => 'https://api.deepseek.com/v1/chat/completions',
                'config_params' => [
                    'api_key' => env('DEEPSEEK_API_KEY', ''),
                    'base_url' => 'https://api.deepseek.com',
                    'version' => 'v1'
                ],
                'capabilities' => ['code_generation', 'code_completion', 'code_review'],
                'is_active' => true,
                'is_default' => false,
                'priority' => 90,
                'cost_per_request' => 0.0014,
                'max_tokens' => 4000,
                'timeout_seconds' => 30,
                'rate_limits' => [
                    'requests_per_minute' => 60,
                    'tokens_per_minute' => 100000
                ],
                'health_status' => 'healthy'
            ],

            // LibLib 模型
            [
                'platform' => 'liblib',
                'model_name' => 'liblib-sd-xl',
                'model_type' => 'image_generation',
                'api_endpoint' => 'https://api.liblib.art/v1/images/generations',
                'config_params' => [
                    'api_key' => env('LIBLIB_API_KEY', ''),
                    'base_url' => 'https://api.liblib.art',
                    'version' => 'v1'
                ],
                'capabilities' => ['text_to_image', 'image_to_image', 'style_transfer'],
                'is_active' => true,
                'is_default' => true,
                'priority' => 100,
                'cost_per_request' => 0.02,
                'max_tokens' => null,
                'timeout_seconds' => 60,
                'rate_limits' => [
                    'requests_per_minute' => 30,
                    'images_per_hour' => 100
                ],
                'health_status' => 'healthy'
            ],

            // Kling 模型
            [
                'platform' => 'kling',
                'model_name' => 'kling-v1',
                'model_type' => 'video_generation',
                'api_endpoint' => 'https://api.kling.kuaishou.com/v1/videos/generations',
                'config_params' => [
                    'api_key' => env('KLING_API_KEY', ''),
                    'base_url' => 'https://api.kling.kuaishou.com',
                    'version' => 'v1'
                ],
                'capabilities' => ['text_to_video', 'image_to_video', 'video_extension'],
                'is_active' => true,
                'is_default' => true,
                'priority' => 100,
                'cost_per_request' => 0.1,
                'max_tokens' => null,
                'timeout_seconds' => 300,
                'rate_limits' => [
                    'requests_per_minute' => 10,
                    'videos_per_hour' => 20
                ],
                'health_status' => 'healthy'
            ],

            // MiniMax 模型
            [
                'platform' => 'minimax',
                'model_name' => 'speech-01',
                'model_type' => 'voice_synthesis',
                'api_endpoint' => 'https://api.minimax.chat/v1/text_to_speech',
                'config_params' => [
                    'api_key' => env('MINIMAX_API_KEY', ''),
                    'group_id' => env('MINIMAX_GROUP_ID', ''),
                    'base_url' => 'https://api.minimax.chat',
                    'version' => 'v1'
                ],
                'capabilities' => ['text_to_speech', 'voice_cloning', 'emotion_control'],
                'is_active' => true,
                'is_default' => true,
                'priority' => 100,
                'cost_per_request' => 0.005,
                'max_tokens' => 1000,
                'timeout_seconds' => 60,
                'rate_limits' => [
                    'requests_per_minute' => 30,
                    'characters_per_minute' => 10000
                ],
                'health_status' => 'healthy'
            ],

            // VolcEngine 模型
            [
                'platform' => 'volcengine',
                'model_name' => 'doubao-lite-4k',
                'model_type' => 'text_generation',
                'api_endpoint' => 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
                'config_params' => [
                    'api_key' => env('VOLCENGINE_API_KEY', ''),
                    'base_url' => 'https://ark.cn-beijing.volces.com',
                    'version' => 'v3'
                ],
                'capabilities' => ['chat', 'completion', 'function_calling'],
                'is_active' => true,
                'is_default' => false,
                'priority' => 80,
                'cost_per_request' => 0.0008,
                'max_tokens' => 4000,
                'timeout_seconds' => 30,
                'rate_limits' => [
                    'requests_per_minute' => 100,
                    'tokens_per_minute' => 200000
                ],
                'health_status' => 'healthy'
            ],

            // 音乐生成模型
            [
                'platform' => 'minimax',
                'model_name' => 'music-01',
                'model_type' => 'music_generation',
                'api_endpoint' => 'https://api.minimax.chat/v1/music_generation',
                'config_params' => [
                    'api_key' => env('MINIMAX_API_KEY', ''),
                    'group_id' => env('MINIMAX_GROUP_ID', ''),
                    'base_url' => 'https://api.minimax.chat',
                    'version' => 'v1'
                ],
                'capabilities' => ['text_to_music', 'style_control', 'duration_control'],
                'is_active' => true,
                'is_default' => true,
                'priority' => 100,
                'cost_per_request' => 0.05,
                'max_tokens' => 500,
                'timeout_seconds' => 120,
                'rate_limits' => [
                    'requests_per_minute' => 10,
                    'music_per_hour' => 30
                ],
                'health_status' => 'healthy'
            ],

            // 音效生成模型
            [
                'platform' => 'volcengine',
                'model_name' => 'sound-effect-v1',
                'model_type' => 'sound_generation',
                'api_endpoint' => 'https://openspeech.bytedance.com/api/v1/sound_effects',
                'config_params' => [
                    'api_key' => env('VOLCENGINE_API_KEY', ''),
                    'base_url' => 'https://openspeech.bytedance.com',
                    'version' => 'v1'
                ],
                'capabilities' => ['text_to_sound', 'ambient_sound', 'effect_sound'],
                'is_active' => true,
                'is_default' => true,
                'priority' => 100,
                'cost_per_request' => 0.01,
                'max_tokens' => 200,
                'timeout_seconds' => 60,
                'rate_limits' => [
                    'requests_per_minute' => 20,
                    'sounds_per_hour' => 50
                ],
                'health_status' => 'healthy'
            ]
        ];

        foreach ($models as $model) {
            AiModelConfig::create($model);
        }
    }
}
