<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_model_configs', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('platform', 50)->comment('AI平台标识');
            $table->string('model_name', 100)->comment('模型名称');
            $table->string('model_type', 50)->comment('模型类型');
            $table->string('api_endpoint', 500)->comment('API端点');
            $table->json('config_params')->comment('配置参数');
            $table->json('capabilities')->comment('模型能力');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->boolean('is_default')->default(false)->comment('是否默认');
            $table->integer('priority')->default(0)->comment('优先级');
            
            // 🔧 修正：字段精度符合现有规范
            $table->decimal('cost_per_request', 8, 4)->default(0.0000)->comment('每次请求成本');
            $table->integer('max_tokens')->default(4000)->comment('最大令牌数');
            $table->integer('timeout_seconds')->nullable()->default(30)->comment('超时时间(秒)');
            
            $table->json('rate_limits')->nullable()->comment('速率限制');
            $table->json('performance_metrics')->nullable()->comment('性能指标');
            $table->timestamp('last_health_check')->nullable()->comment('最后健康检查时间');
            $table->enum('health_status', ['healthy', 'degraded', 'unhealthy', 'unknown'])
                  ->default('unknown')->comment('健康状态');
            $table->text('health_message')->nullable()->comment('健康状态消息');
            
            // 🔧 新增：软删除支持
            $table->softDeletes()->comment('软删除时间');
            $table->timestamps();

            // 索引优化
            $table->index(['platform', 'model_type', 'is_active'], 'idx_platform_type_active');
            $table->index(['model_type', 'priority', 'is_active'], 'idx_type_priority_active');
            $table->index(['is_default', 'model_type'], 'idx_default_type');
            $table->index('health_status', 'idx_health_status');
            $table->index('deleted_at', 'idx_deleted_at'); // 软删除索引
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_model_configs');
    }
};
