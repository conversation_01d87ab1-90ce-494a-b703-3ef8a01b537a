<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\DownloadManagementService;
use Illuminate\Http\Request;

/**
 * 下载控制与配额管理
 */
class DownloadController extends Controller
{
    protected $downloadService;

    public function __construct(DownloadManagementService $downloadService)
    {
        $this->downloadService = $downloadService;
    }

    /**
     * @ApiTitle (下载历史列表)
     * @ApiSummary (获取用户的下载历史记录)
     * @ApiMethod (GET)
     * @ApiRoute (/api/downloads/list)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="download_type", type="string", required=false, description="下载类型：resource/export/version")
     * @ApiParams (name="status", type="string", required=false, description="状态过滤：completed/failed/expired")
     * @ApiParams (name="date_from", type="string", required=false, description="开始日期：YYYY-MM-DD")
     * @ApiParams (name="date_to", type="string", required=false, description="结束日期：YYYY-MM-DD")
     * @ApiParams (name="page", type="int", required=false, description="页码")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "downloads": [
     *       {
     *         "download_id": 123,
     *         "download_type": "resource",
     *         "target_id": 456,
     *         "target_name": "我的故事.json",
     *         "file_size": "2.5MB",
     *         "status": "completed",
     *         "download_url": "https://aiapi.tiptop.cn/downloads/123",
     *         "downloaded_at": "2024-01-01 12:00:00",
     *         "expires_at": "2024-01-08 12:00:00",
     *         "user_agent": "Mozilla/5.0...",
     *         "ip_address": "***********"
     *       }
     *     ],
     *     "statistics": {
     *       "total_downloads": 50,
     *       "total_size": "125.6MB",
     *       "this_month": 15,
     *       "success_rate": 95.5
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 50,
     *       "last_page": 3
     *     }
     *   }
     * })
     */
    public function list(Request $request)
    {
        $rules = [
            'download_type' => 'sometimes|string|in:resource,export,version',
            'status' => 'sometimes|string|in:completed,failed,expired',
            'date_from' => 'sometimes|date_format:Y-m-d',
            'date_to' => 'sometimes|date_format:Y-m-d|after_or_equal:date_from',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];

        $messages = [
            'download_type.in' => '下载类型必须是：resource、export、version之一',
            'status.in' => '状态必须是：completed、failed、expired之一',
            'date_from.date_format' => '开始日期格式不正确，应为：YYYY-MM-DD',
            'date_to.date_format' => '结束日期格式不正确，应为：YYYY-MM-DD',
            'date_to.after_or_equal' => '结束日期不能早于开始日期'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $filters = [
            'download_type' => $request->get('download_type'),
            'status' => $request->get('status'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->downloadService->getDownloadHistory($user->id, $filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (重试下载任务)
     * @ApiSummary (重新尝试失败的下载任务)
     * @ApiMethod (POST)
     * @ApiRoute (/api/downloads/{id}/retry)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="下载记录ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "下载重试成功",
     *   "data": {
     *     "download_id": 123,
     *     "new_download_url": "https://aiapi.tiptop.cn/downloads/123?retry=1",
     *     "status": "ready",
     *     "expires_at": "2024-01-08 12:00:00",
     *     "retry_count": 2
     *   }
     * })
     */
    public function retry(Request $request, $downloadId)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->downloadService->retryDownload($downloadId, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (获取下载统计)
     * @ApiSummary (获取用户的下载统计信息)
     * @ApiMethod (GET)
     * @ApiRoute (/api/downloads/statistics)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="period", type="string", required=false, description="统计周期：day/week/month/year")
     * @ApiParams (name="download_type", type="string", required=false, description="下载类型过滤")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "period": "month",
     *     "total_downloads": 150,
     *     "successful_downloads": 143,
     *     "failed_downloads": 7,
     *     "success_rate": 95.33,
     *     "total_size": "2.5GB",
     *     "average_size": "17.1MB",
     *     "most_downloaded_type": "resource",
     *     "download_trends": [
     *       {"date": "2024-01-01", "count": 5, "size": "85.2MB"},
     *       {"date": "2024-01-02", "count": 8, "size": "142.6MB"}
     *     ],
     *     "type_breakdown": {
     *       "resource": {"count": 80, "percentage": 53.3},
     *       "export": {"count": 45, "percentage": 30.0},
     *       "version": {"count": 25, "percentage": 16.7}
     *     }
     *   }
     * })
     */
    public function statistics(Request $request)
    {
        $rules = [
            'period' => 'sometimes|string|in:day,week,month,year',
            'download_type' => 'sometimes|string|in:resource,export,version'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $params = [
            'period' => $request->get('period', 'month'),
            'download_type' => $request->get('download_type')
        ];

        $result = $this->downloadService->getDownloadStatistics($user->id, $params);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (创建下载链接)
     * @ApiSummary (为指定资源创建安全的下载链接)
     * @ApiMethod (POST)
     * @ApiRoute (/api/downloads/create-link)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="target_type", type="string", required=true, description="目标类型：resource/export/version")
     * @ApiParams (name="target_id", type="int", required=true, description="目标ID")
     * @ApiParams (name="expires_in", type="int", required=false, description="链接有效期（秒）")
     * @ApiParams (name="download_name", type="string", required=false, description="自定义下载文件名")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "下载链接创建成功",
     *   "data": {
     *     "download_id": 123,
     *     "download_url": "https://aiapi.tiptop.cn/downloads/secure/abc123def456",
     *     "target_type": "resource",
     *     "target_id": 456,
     *     "file_name": "我的故事.json",
     *     "file_size": "2.5MB",
     *     "expires_at": "2024-01-01 18:00:00",
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function createLink(Request $request)
    {
        $rules = [
            'target_type' => 'required|string|in:resource,export,version',
            'target_id' => 'required|integer',
            'expires_in' => 'sometimes|integer|min:300|max:86400', // 5分钟到24小时
            'download_name' => 'sometimes|string|max:255'
        ];

        $messages = [
            'target_type.required' => '目标类型不能为空',
            'target_type.in' => '目标类型必须是：resource、export、version之一',
            'target_id.required' => '目标ID不能为空',
            'target_id.integer' => '目标ID必须是整数',
            'expires_in.min' => '链接有效期不能少于5分钟',
            'expires_in.max' => '链接有效期不能超过24小时',
            'download_name.max' => '文件名不能超过255个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $linkParams = [
            'target_type' => $request->target_type,
            'target_id' => $request->target_id,
            'expires_in' => $request->get('expires_in', 3600), // 默认1小时
            'download_name' => $request->get('download_name')
        ];

        $result = $this->downloadService->createDownloadLink($user->id, $linkParams);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (安全下载)
     * @ApiSummary (通过安全令牌下载文件)
     * @ApiMethod (GET)
     * @ApiRoute (/api/downloads/secure/{token})
     * @ApiParams (name="token", type="string", required=true, description="下载令牌")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn (文件下载响应)
     */
    public function secureDownload(Request $request, $token)
    {
        $result = $this->downloadService->secureDownload($token);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (批量下载)
     * @ApiSummary (创建多个资源的批量下载任务)
     * @ApiMethod (POST)
     * @ApiRoute (/api/downloads/batch)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="items", type="array", required=true, description="下载项目数组")
     * @ApiParams (name="archive_name", type="string", required=false, description="压缩包名称")
     * @ApiParams (name="compression_level", type="string", required=false, description="压缩级别")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "批量下载任务创建成功",
     *   "data": {
     *     "batch_id": "batch_123",
     *     "download_url": "https://aiapi.tiptop.cn/downloads/batch/batch_123",
     *     "item_count": 5,
     *     "estimated_size": "25.6MB",
     *     "status": "preparing",
     *     "expires_at": "2024-01-08 12:00:00"
     *   }
     * })
     */
    public function batchDownload(Request $request)
    {
        $rules = [
            'items' => 'required|array|min:1|max:20',
            'items.*.target_type' => 'required|string|in:resource,export,version',
            'items.*.target_id' => 'required|integer',
            'archive_name' => 'sometimes|string|max:100',
            'compression_level' => 'sometimes|string|in:none,low,medium,high'
        ];

        $messages = [
            'items.required' => '下载项目数组不能为空',
            'items.min' => '至少需要1个下载项目',
            'items.max' => '最多支持20个下载项目',
            'items.*.target_type.required' => '目标类型不能为空',
            'items.*.target_type.in' => '目标类型必须是：resource、export、version之一',
            'items.*.target_id.required' => '目标ID不能为空',
            'archive_name.max' => '压缩包名称不能超过100个字符',
            'compression_level.in' => '压缩级别必须是：none、low、medium、high之一'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $batchParams = [
            'items' => $request->items,
            'archive_name' => $request->get('archive_name', '批量下载_' . date('YmdHis')),
            'compression_level' => $request->get('compression_level', 'medium')
        ];

        $result = $this->downloadService->createBatchDownload($user->id, $batchParams);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (清理过期下载)
     * @ApiSummary (清理用户的过期下载记录和文件)
     * @ApiMethod (POST)
     * @ApiRoute (/api/downloads/cleanup)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="days_old", type="int", required=false, description="清理多少天前的记录")
     * @ApiParams (name="cleanup_files", type="boolean", required=false, description="是否同时删除文件")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "下载记录清理完成",
     *   "data": {
     *     "cleaned_records": 25,
     *     "deleted_files": 20,
     *     "freed_space": "156.8MB",
     *     "cleanup_date": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function cleanup(Request $request)
    {
        $rules = [
            'days_old' => 'sometimes|integer|min:1|max:365',
            'cleanup_files' => 'sometimes|boolean'
        ];

        $messages = [
            'days_old.min' => '清理天数不能少于1天',
            'days_old.max' => '清理天数不能超过365天'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $cleanupParams = [
            'days_old' => $request->get('days_old', 30), // 默认30天
            'cleanup_files' => $request->get('cleanup_files', true)
        ];

        $result = $this->downloadService->cleanupDownloads($user->id, $cleanupParams);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
