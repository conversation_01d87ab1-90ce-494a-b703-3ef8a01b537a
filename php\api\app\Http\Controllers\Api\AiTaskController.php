<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\AiTaskService;
use Illuminate\Http\Request;

/**
 * AI任务管理控制器
 * 处理AI生成任务的创建、查询、重试等操作
 */
class AiTaskController extends Controller
{
    protected $aiTaskService;

    public function __construct(AiTaskService $aiTaskService)
    {
        $this->aiTaskService = $aiTaskService;
    }

    /**
     * @ApiTitle(获取AI任务列表)
     * @ApiSummary(查询用户的AI任务列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai/tasks)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="type", type="string", required=false, description="任务类型：image,story,video,voice,music,sound")
     * @ApiParams(name="status", type="string", required=false, description="任务状态：pending,processing,completed,failed")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="integer", required=false, description="每页数量，默认20")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "tasks": [
     *       {
     *         "id": 1,
     *         "type": "image",
     *         "status": "completed",
     *         "prompt": "一只可爱的小猫",
     *         "result_url": "https://api.tiptop.cn/files/123.jpg",
     *         "progress": 100,
     *         "created_at": "2024-01-01 12:00:00",
     *         "completed_at": "2024-01-01 12:05:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 50,
     *       "last_page": 3
     *     }
     *   }
     * })
     */
    public function index(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'type' => 'sometimes|string|in:image,story,video,voice,music,sound',
            'status' => 'sometimes|string|in:pending,processing,completed,failed',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];

        $this->validateData($request->all(), $rules);

        $filters = [
            'type' => $request->get('type'),
            'status' => $request->get('status'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->aiTaskService->getUserTasks($user->id, $filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(获取AI任务详情)
     * @ApiSummary(查询指定AI任务的详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai/tasks/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="任务ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 1,
     *     "type": "image",
     *     "status": "completed",
     *     "prompt": "一只可爱的小猫",
     *     "parameters": {
     *       "style": "cartoon",
     *       "size": "1024x1024",
     *       "quality": "high"
     *     },
     *     "result_url": "https://api.tiptop.cn/files/123.jpg",
     *     "progress": 100,
     *     "error_message": null,
     *     "ai_platform": "liblib",
     *     "cost_points": 10,
     *     "created_at": "2024-01-01 12:00:00",
     *     "started_at": "2024-01-01 12:01:00",
     *     "completed_at": "2024-01-01 12:05:00"
     *   }
     * })
     */
    public function show($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $result = $this->aiTaskService->getTaskDetail($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(重试AI任务)
     * @ApiSummary(重新执行失败的AI任务)
     * @ApiMethod(POST)
     * @ApiRoute(/api/ai/tasks/{id}/retry)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="任务ID")
     * @ApiParams(name="use_different_platform", type="boolean", required=false, description="是否使用不同的AI平台")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "任务重试成功",
     *   "data": {
     *     "task_id": 1,
     *     "new_task_id": 123,
     *     "status": "pending",
     *     "estimated_time": 300,
     *     "retry_count": 2
     *   }
     * })
     */
    public function retry($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'use_different_platform' => 'sometimes|boolean'
        ];

        $this->validateData($request->all(), $rules);

        $options = [
            'use_different_platform' => $request->get('use_different_platform', false)
        ];

        $result = $this->aiTaskService->retryTask($id, $user->id, $options);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(取消AI任务)
     * @ApiSummary(取消正在进行的AI任务)
     * @ApiMethod(DELETE)
     * @ApiRoute(/api/ai/tasks/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="任务ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "任务取消成功",
     *   "data": {
     *     "task_id": 1,
     *     "status": "cancelled",
     *     "refund_points": 5
     *   }
     * })
     */
    public function cancel($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $result = $this->aiTaskService->cancelTask($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(获取任务统计)
     * @ApiSummary(获取用户的AI任务统计信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai/tasks/stats)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="period", type="string", required=false, description="统计周期：today,week,month,all")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "total_tasks": 150,
     *     "completed_tasks": 120,
     *     "failed_tasks": 20,
     *     "pending_tasks": 10,
     *     "total_points_used": 1500,
     *     "success_rate": 80.0,
     *     "by_type": {
     *       "image": 80,
     *       "story": 30,
     *       "video": 20,
     *       "voice": 15,
     *       "music": 3,
     *       "sound": 2
     *     }
     *   }
     * })
     */
    public function stats(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'period' => 'sometimes|string|in:today,week,month,all'
        ];

        $this->validateData($request->all(), $rules);

        $period = $request->get('period', 'all', []);

        $result = $this->aiTaskService->getTaskStats($user->id, $period);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
