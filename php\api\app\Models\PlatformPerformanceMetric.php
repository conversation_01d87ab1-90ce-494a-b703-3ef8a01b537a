<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

/**
 * 平台性能指标模型
 * 
 * @property int $id
 * @property string $platform
 * @property string $business_type
 * @property float $response_time_avg
 * @property float $success_rate
 * @property float $cost_score
 * @property float $quality_score
 * @property int $total_requests
 * @property int $failed_requests
 * @property float $uptime_percentage
 * @property array $detailed_metrics
 * @property string $metric_date
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class PlatformPerformanceMetric extends Model
{
    /**
     * 表名
     */
    protected $table = 'platform_performance_metrics';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'platform',
        'business_type',
        'response_time_avg',
        'success_rate',
        'cost_score',
        'quality_score',
        'total_requests',
        'failed_requests',
        'uptime_percentage',
        'detailed_metrics',
        'metric_date'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'response_time_avg' => 'decimal:3',
        'success_rate' => 'decimal:4',
        'cost_score' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'total_requests' => 'integer',
        'failed_requests' => 'integer',
        'uptime_percentage' => 'decimal:2',
        'detailed_metrics' => 'array',
        'metric_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 平台常量
     */
    const PLATFORM_DEEPSEEK = 'deepseek';
    const PLATFORM_LIBLIB = 'liblib';
    const PLATFORM_KLING = 'kling';
    const PLATFORM_MINIMAX = 'minimax';
    const PLATFORM_VOLCENGINE = 'volcengine';

    /**
     * 业务类型常量
     */
    const BUSINESS_TYPE_IMAGE = 'image';
    const BUSINESS_TYPE_VIDEO = 'video';
    const BUSINESS_TYPE_STORY = 'story';
    const BUSINESS_TYPE_CHARACTER = 'character';
    const BUSINESS_TYPE_STYLE = 'style';
    const BUSINESS_TYPE_VOICE = 'voice';
    const BUSINESS_TYPE_SOUND = 'sound';
    const BUSINESS_TYPE_MUSIC = 'music';

    /**
     * 获取所有支持的平台
     */
    public static function getSupportedPlatforms(): array
    {
        return [
            self::PLATFORM_DEEPSEEK,
            self::PLATFORM_LIBLIB,
            self::PLATFORM_KLING,
            self::PLATFORM_MINIMAX,
            self::PLATFORM_VOLCENGINE
        ];
    }

    /**
     * 获取所有业务类型
     */
    public static function getBusinessTypes(): array
    {
        return [
            self::BUSINESS_TYPE_IMAGE,
            self::BUSINESS_TYPE_VIDEO,
            self::BUSINESS_TYPE_STORY,
            self::BUSINESS_TYPE_CHARACTER,
            self::BUSINESS_TYPE_STYLE,
            self::BUSINESS_TYPE_VOICE,
            self::BUSINESS_TYPE_SOUND,
            self::BUSINESS_TYPE_MUSIC
        ];
    }

    /**
     * 计算失败率
     */
    public function getFailureRateAttribute(): float
    {
        if ($this->total_requests == 0) {
            return 0.0;
        }
        return ($this->failed_requests / $this->total_requests) * 100;
    }

    /**
     * 计算成功请求数
     */
    public function getSuccessfulRequestsAttribute(): int
    {
        return $this->total_requests - $this->failed_requests;
    }

    /**
     * 获取性能等级
     */
    public function getPerformanceGradeAttribute(): string
    {
        $score = ($this->success_rate + $this->quality_score * 10 + $this->uptime_percentage) / 3;
        
        if ($score >= 95) return 'A+';
        if ($score >= 90) return 'A';
        if ($score >= 85) return 'B+';
        if ($score >= 80) return 'B';
        if ($score >= 75) return 'C+';
        if ($score >= 70) return 'C';
        return 'D';
    }

    /**
     * 是否为高性能平台
     */
    public function isHighPerformance(): bool
    {
        return $this->success_rate >= 95 && 
               $this->response_time_avg <= 3.0 && 
               $this->uptime_percentage >= 99.0;
    }

    /**
     * 是否需要关注
     */
    public function needsAttention(): bool
    {
        return $this->success_rate < 90 || 
               $this->response_time_avg > 10.0 || 
               $this->uptime_percentage < 95.0;
    }

    /**
     * 更新详细指标
     */
    public function updateDetailedMetrics(array $metrics): void
    {
        $currentMetrics = $this->detailed_metrics ?? [];
        $this->detailed_metrics = array_merge($currentMetrics, $metrics);
        $this->save();
    }

    /**
     * 获取详细指标
     */
    public function getDetailedMetric(string $key, $default = null)
    {
        return $this->detailed_metrics[$key] ?? $default;
    }

    /**
     * 范围查询：按平台
     */
    public function scopeByPlatform($query, string $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * 范围查询：按业务类型
     */
    public function scopeByBusinessType($query, string $businessType)
    {
        return $query->where('business_type', $businessType);
    }

    /**
     * 范围查询：按日期范围
     */
    public function scopeByDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('metric_date', [$startDate->toDateString(), $endDate->toDateString()]);
    }

    /**
     * 范围查询：最近N天
     */
    public function scopeRecentDays($query, int $days)
    {
        return $query->where('metric_date', '>=', now()->subDays($days)->toDateString());
    }

    /**
     * 范围查询：高性能平台
     */
    public function scopeHighPerformance($query)
    {
        return $query->where('success_rate', '>=', 95)
                    ->where('response_time_avg', '<=', 3.0)
                    ->where('uptime_percentage', '>=', 99.0);
    }

    /**
     * 范围查询：需要关注的平台
     */
    public function scopeNeedsAttention($query)
    {
        return $query->where(function ($q) {
            $q->where('success_rate', '<', 90)
              ->orWhere('response_time_avg', '>', 10.0)
              ->orWhere('uptime_percentage', '<', 95.0);
        });
    }

    /**
     * 范围查询：按成功率排序
     */
    public function scopeOrderBySuccessRate($query, string $direction = 'desc')
    {
        return $query->orderBy('success_rate', $direction);
    }

    /**
     * 范围查询：按响应时间排序
     */
    public function scopeOrderByResponseTime($query, string $direction = 'asc')
    {
        return $query->orderBy('response_time_avg', $direction);
    }

    /**
     * 创建或更新指标
     */
    public static function createOrUpdateMetric(
        string $platform,
        string $businessType,
        array $metrics,
        ?string $date = null
    ): self {
        $date = $date ?? now()->toDateString();
        
        return self::updateOrCreate(
            [
                'platform' => $platform,
                'business_type' => $businessType,
                'metric_date' => $date
            ],
            $metrics
        );
    }

    /**
     * 获取平台排名
     */
    public static function getPlatformRanking(string $businessType, int $days = 7): array
    {
        return self::byBusinessType($businessType)
            ->recentDays($days)
            ->selectRaw('
                platform,
                AVG(success_rate) as avg_success_rate,
                AVG(response_time_avg) as avg_response_time,
                AVG(quality_score) as avg_quality_score,
                AVG(uptime_percentage) as avg_uptime
            ')
            ->groupBy('platform')
            ->orderByDesc('avg_success_rate')
            ->orderBy('avg_response_time')
            ->get()
            ->toArray();
    }
}
