﻿步骤1: 10.1 获取剧情风格列表    GET /api/styles/list
步骤1: 17.1 绑定角色            POST /api/characters/bind
步骤1: 18.1 角色分类列表        GET /api/characters/categories
步骤1: 2.1 获取可用模型        GET /api/ai-models/available
步骤1: 21.1 积分余额查询        GET /api/points/balance
步骤1: 24.1 用户中心信息        GET /api/user/profile
步骤1: 24.2 更新用户资料         PUT /api/user/profile
步骤1: 36.1 智能图像生成        POST /api/images/generate
步骤1: 38.1 音乐生成            POST /api/music/generate (测试生成功能，可生成多条数据验证)
步骤1: 39.1 选风格+写剧情创建项目 POST /api/projects/create-with-story
步骤1: 46.1 音效生成            POST /api/sounds/generate (测试生成功能，可生成多条数据验证)
步骤1: 47.1 智能视频生成        POST /api/videos/generate
步骤1: 48.1 智能语音合成        POST /api/voices/synthesize (测试合成功能，可合成多条数据验证)
步骤1: 49.5 作品展示库          GET /api/works/gallery
步骤1: 5.1 生成验证码          GET /api/captcha/generate
步骤1: 7.4 WebSocket服务状态    GET /api/websocket/status
步骤1: 8.1 系统健康检查          GET /api/system/health
步骤1: 9.1 获取超时配置          GET /api/tasks/timeout-config
步骤10: 11.4 应用告警列表        GET /api/app-monitor/alerts
步骤10: 13.5 获取任务统计       GET /api/ai/tasks/stats
步骤10: 4.6 重置密码             POST /api/reset-password
步骤10: 4.6 重置密码           POST /api/reset-password
步骤10: 48.10 语音合成历史      GET /api/voices/history
步骤11: 11.5 确认告警            PUT /api/app-monitor/alerts/{id}/acknowledge
步骤11: 13.6 创建AI任务         POST /api/ai/tasks
步骤11: 7.2 获取WebSocket会话列表 GET /api/websocket/sessions
步骤12: 11.6 解决告警            PUT /api/app-monitor/alerts/{id}/resolve
步骤12: 7.3 断开WebSocket连接    POST /api/websocket/disconnect
步骤13: 19.1 获取系统配置        GET /api/config/system
步骤14: 19.2 更新系统配置        PUT /api/config/system
步骤15: 19.3 获取用户配置        GET /api/config/user
步骤16: 19.4 更新用户配置        PUT /api/config/user
步骤17: 19.5 获取AI配置          GET /api/config/ai
步骤18: 19.6 更新AI配置          PUT /api/config/ai
步骤19: 19.7 重置配置            POST /api/config/reset
步骤2: 10.2 获取风格详情        GET /api/styles/{id}
步骤2: 17.3 获取绑定列表        GET /api/characters/bindings
步骤2: 18.2 角色列表            GET /api/characters/list
步骤2: 2.2 获取模型详情        GET /api/ai-models/{model_id}/detail
步骤2: 21.2 积分充值            POST /api/points/recharge
步骤2: 23.1 获取用户权限        GET /api/permissions/user
步骤2: 24.3 用户偏好设置         PUT /api/user/preferences
步骤2: 26.1 取消任务            POST /api/tasks/{id}/cancel
步骤2: 28.1 故事生成            POST /api/stories/generate
步骤2: 36.2 获取图像生成状态    GET /api/images/{task_id}/status
步骤2: 38.2 获取音乐生成状态    GET /api/music/{task_id}/status
步骤2: 41.7 作品广场            GET /api/publications/plaza
步骤2: 46.2 音效生成状态查询    GET /api/sounds/{id}/status
步骤2: 47.2 获取视频生成状态    GET /api/videos/{task_id}/status
步骤2: 48.2 获取语音合成状态    GET /api/voices/{task_id}/status
步骤2: 5.2 验证验证码          POST /api/captcha/verify
步骤2: 7.1 WebSocket连接认证    POST /api/websocket/auth
步骤2: 8.2 性能指标监控          GET /api/system/metrics
步骤20: 6.1 获取缓存统计         GET /api/cache/stats
步骤21: 6.2 获取缓存键列表       GET /api/cache/keys
步骤22: 6.3 获取缓存值           GET /api/cache/get
步骤23: 6.4 获取缓存配置         GET /api/cache/config
步骤24: 16.1 清理缓存            DELETE /api/cache/clear
步骤25: 16.2 预热缓存            POST /api/cache/warmup
步骤26: 16.3 设置缓存值          PUT /api/cache/set
步骤27: 16.4 删除缓存键          DELETE /api/cache/delete
步骤3: 10.3 获取热门风格        GET /api/styles/popular
步骤3: 17.5 获取绑定详情        GET /api/characters/bindings/{id}
步骤3: 18.3 获取角色详情        GET /api/characters/{id}
步骤3: 2.3 获取收藏模型        GET /api/ai-models/favorites
步骤3: 21.3 积分交易记录        GET /api/points/transactions
步骤3: 23.2 检查用户权限        POST /api/permissions/check
步骤3: 23.3 获取角色列表         GET /api/permissions/roles
步骤3: 26.2 重试任务            POST /api/tasks/{id}/retry
步骤3: 28.2 故事生成状态查询    GET /api/stories/{id}/status
步骤3: 36.5 图像生成结果获取    GET /api/images/{id}/result
步骤3: 38.3 音乐风格列表        GET /api/music/styles
步骤3: 41.9 热门作品            GET /api/publications/trending
步骤3: 46.3 音效生成结果获取    GET /api/sounds/{id}/result
步骤3: 47.4 视频生成结果获取    GET /api/videos/{id}/result
步骤3: 48.3 语音平台对比        GET /api/voices/platform-comparison
步骤3: 5.3 刷新验证码          POST /api/captcha/refresh
步骤3: 8.3 响应时间监控          GET /api/system/response-time
步骤4: 10.4 创建风格            POST /api/styles/create (测试创建功能，可创建多条数据验证)
步骤4: 18.4 推荐角色            GET /api/characters/recommendations
步骤4: 2.4 模型列表            GET /api/ai-models/list
步骤4: 20.1 积分预检查          POST /api/credits/check
步骤4: 23.4 分配用户角色         PUT /api/permissions/assign-role
步骤4: 24.4 获取用户偏好设置    GET /api/user/preferences
步骤4: 26.3 批量任务状态查询    GET /api/batch/tasks/status
步骤4: 38.4 音乐生成结果获取    GET /api/music/{id}/result
步骤4: 39.2 确认AI生成的项目标题 PUT /api/projects/{id}/confirm-title
步骤4: 4.1 用户注册            POST /api/register
步骤4: 41.8 获取作品详情        GET /api/publications/{id}/detail
步骤4: 46.4 批量音效生成        POST /api/batch/sounds/generate
步骤4: 48.4 批量语音合成        POST /api/voices/batch-synthesize
步骤4: 8.4 系统监控概览          GET /api/system/monitor/overview
步骤5: 18.5 角色绑定            POST /api/characters/bind
步骤5: 2.5 智能平台切换        POST /api/ai-models/switch
步骤5: 20.2 积分冻结            POST /api/credits/freeze
步骤5: 23.5 授予用户权限         POST /api/permissions/grant
步骤5: 26.4 查询任务恢复状态    GET /api/tasks/{id}/recovery
步骤5: 38.5 批量音乐生成        POST /api/batch/music/generate
步骤5: 39.3 获取用户项目列表    GET /api/projects/my-projects
步骤5: 4.2 用户登录            POST /api/login
步骤5: 48.5 音色克隆            POST /api/voices/clone
步骤5: 49.1 发布作品            POST /api/works/publish
步骤5: 8.5 系统性能指标          GET /api/system/monitor/metrics
步骤6: 13.1 获取AI任务列表      GET /api/ai/tasks
步骤6: 18.6 获取我的角色绑定    GET /api/characters/my-bindings
步骤6: 2.6 平台性能对比        GET /api/ai-models/platform-comparison
步骤6: 20.3 积分返还            POST /api/credits/refund
步骤6: 23.6 撤销用户权限         DELETE /api/permissions/revoke
步骤6: 39.4 获取项目详情        GET /api/projects/{id}
步骤6: 4.7 验证Token           GET /api/verify
步骤6: 41.1 发布作品            POST /api/publications/publish
步骤6: 48.6 音色克隆状态查询    GET /api/voices/clone/{id}/status
步骤6: 8.6 全局搜索              GET /api/system/search
步骤7: 11.1 应用健康检查         GET /api/app-monitor/health
步骤7: 13.2 获取AI任务详情      GET /api/ai/tasks/{id}
步骤7: 18.7 更新角色绑定        PUT /api/characters/bindings/{id}
步骤7: 2.7 按业务类型获取可选平台 GET /api/ai-models/business-platforms
步骤7: 23.7 获取权限历史         GET /api/permissions/history
步骤7: 4.4 刷新Token           POST /api/refresh
步骤7: 48.7 自定义音色生成      POST /api/voices/custom
步骤7: 49.4 获取我的作品        GET /api/works/my-works
步骤8: 11.2 应用性能指标         GET /api/app-monitor/metrics
步骤8: 13.3 重试AI任务          POST /api/ai/tasks/{id}/retry
步骤8: 18.8 解绑角色            DELETE /api/characters/unbind
步骤8: 4.3 用户登出            POST /api/logout
步骤8: 41.6 我的发布列表        GET /api/publications/my-publications
步骤8: 48.8 自定义音色状态查询  GET /api/voices/custom/{id}/status
步骤8: 5.3 刷新验证码            POST /api/captcha/refresh
步骤9: 11.3 实时监控数据         GET /api/app-monitor/realtime
步骤9: 13.4 取消AI任务          DELETE /api/ai/tasks/{id}
步骤9: 27.1 角色生成            POST /api/characters/generate (测试生成功能，可生成多条数据验证)
步骤9: 4.5 忘记密码              POST /api/forgot-password
步骤9: 4.5 忘记密码            POST /api/forgot-password
步骤9: 48.9 音色试听            POST /api/voices/{id}/preview
