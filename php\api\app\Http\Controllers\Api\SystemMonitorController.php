<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\SystemMonitorService;
use App\Services\SearchService;
use Illuminate\Http\Request;

/**
 * 系统监控与健康检查
 */
class SystemMonitorController extends Controller
{
    protected $monitorService;
    protected $searchService;

    public function __construct(SystemMonitorService $monitorService, SearchService $searchService)
    {
        $this->monitorService = $monitorService;
        $this->searchService = $searchService;
    }

    /**
     * @ApiTitle (系统监控概览)
     * @ApiSummary (获取系统监控概览信息)
     * @ApiMethod (GET)
     * @ApiRoute (/api/system/monitor/overview)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="监控概览")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "system_status": "healthy",
     *     "metrics": {
     *       "cpu_usage": 45.2,
     *       "memory_usage": 68.5,
     *       "disk_usage": 32.1,
     *       "active_users": 150,
     *       "api_requests": 1250,
     *       "websocket_connections": 25
     *     },
     *     "alerts": [
     *       {
     *         "type": "warning",
     *         "message": "内存使用率较高",
     *         "value": 68.5,
     *         "threshold": 70
     *       }
     *     ],
     *     "last_updated": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function getMonitorOverview(Request $request)
    {
        $result = $this->monitorService->getSystemOverview();
        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * 系统监控概览 (路由别名)
     * 修复500错误 - 添加缺失的overview方法
     */
    public function overview(Request $request)
    {
        return $this->getMonitorOverview($request);
    }

    /**
     * @ApiTitle (系统性能指标)
     * @ApiSummary (获取系统性能指标详情)
     * @ApiMethod (GET)
     * @ApiRoute (/api/system/monitor/metrics)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="metric_type", type="string", required=false, description="指标类型")
     * @ApiParams (name="time_range", type="string", required=false, description="时间范围：1h,6h,24h,7d")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="性能指标")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "metrics": [
     *       {
     *         "metric_type": "system",
     *         "metric_name": "cpu_usage",
     *         "current_value": 45.2,
     *         "avg_value": 42.8,
     *         "max_value": 78.5,
     *         "min_value": 15.2,
     *         "status": "normal",
     *         "unit": "%"
     *       }
     *     ],
     *     "time_series": [
     *       {
     *         "timestamp": "2024-01-01 12:00:00",
     *         "cpu_usage": 45.2,
     *         "memory_usage": 68.5,
     *         "disk_usage": 32.1
     *       }
     *     ]
     *   }
     * })
     */
    public function getMetrics(Request $request)
    {
        $metricType = $request->get('metric_type');
        $timeRange = $request->get('time_range', '24h');

        $result = $this->monitorService->getMetrics($metricType, $timeRange);
        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (全局搜索)
     * @ApiSummary (全局搜索功能)
     * @ApiMethod (GET)
     * @ApiRoute (/api/system/search)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="keyword", type="string", required=true, description="搜索关键词")
     * @ApiParams (name="type", type="string", required=false, description="搜索类型")
     * @ApiParams (name="limit", type="int", required=false, description="结果数量限制")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="搜索结果")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "results": [
     *       {
     *         "type": "character",
     *         "id": 1,
     *         "title": "小樱",
     *         "description": "可爱的魔法少女",
     *         "url": "/characters/1",
     *         "score": 0.95
     *       },
     *       {
     *         "type": "project",
     *         "id": 123,
     *         "title": "我的项目",
     *         "description": "项目描述",
     *         "url": "/projects/123",
     *         "score": 0.87
     *       }
     *     ],
     *     "total": 25,
     *     "search_time": 0.15
     *   }
     * })
     */
    public function globalSearch(Request $request)
    {
        $rules = [
            'keyword' => 'required|string|min:1|max:100',
            'type' => 'sometimes|string|in:all,characters,projects,files,ai_tasks',
            'limit' => 'sometimes|integer|min:1|max:50'
        ];

        $messages = [
            'keyword.required' => '搜索关键词不能为空',
            'keyword.min' => '搜索关键词至少1个字符',
            'keyword.max' => '搜索关键词不能超过100个字符',
            'type.in' => '搜索类型无效',
            'limit.min' => '结果数量至少为1',
            'limit.max' => '结果数量不能超过50'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->searchService->globalSearch(
            $user->id,
            $request->keyword,
            $request->get('type', 'all'),
            $request->get('limit', 20)
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (系统健康检查)
     * @ApiSummary (检查系统各组件的健康状态)
     * @ApiMethod (GET)
     * @ApiRoute (/api/system/health)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "系统健康检查完成",
     *   "data": {
     *     "status": "healthy",
     *     "timestamp": "2024-01-01 12:00:00",
     *     "components": {
     *       "database": {"status": "healthy", "response_time_ms": 15},
     *       "redis": {"status": "healthy", "response_time_ms": 5},
     *       "storage": {"status": "healthy", "disk_usage": "45%"},
     *       "ai_services": {"status": "healthy", "available_models": 5}
     *     }
     *   }
     * })
     */
    public function health(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $result = $this->monitorService->getSystemHealth();
        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (性能指标监控)
     * @ApiSummary (获取系统性能指标)
     * @ApiMethod (GET)
     * @ApiRoute (/api/system/metrics)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="period", type="string", required=false, description="时间周期：1h/6h/24h/7d")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "性能指标获取成功",
     *   "data": {
     *     "period": "1h",
     *     "metrics": {
     *       "cpu": {"current": 25.5, "average": 22.3, "peak": 45.2},
     *       "memory": {"current": 68.2, "average": 65.8, "peak": 78.9},
     *       "disk": {"current": 45.0, "average": 44.5, "peak": 46.2}
     *     }
     *   }
     * })
     */
    public function metrics(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $period = $request->get('period', '1h');
        $result = $this->monitorService->getSystemMetrics($period);
        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (响应时间监控)
     * @ApiSummary (获取系统响应时间统计)
     * @ApiMethod (GET)
     * @ApiRoute (/api/system/response-time)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="period", type="string", required=false, description="时间周期：1h/6h/24h/7d")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "响应时间统计获取成功",
     *   "data": {
     *     "period": "1h",
     *     "overall": {"average_ms": 125, "median_ms": 98, "p95_ms": 245},
     *     "endpoints": {
     *       "/api/stories/generate": {"average_ms": 2500, "requests": 45},
     *       "/api/images/generate": {"average_ms": 3200, "requests": 32}
     *     }
     *   }
     * })
     */
    public function responseTime(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $period = $request->get('period', '1h');
        $result = $this->monitorService->getResponseTimeStats($period);
        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
