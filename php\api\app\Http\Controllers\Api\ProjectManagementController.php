<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\ProjectManagementService;
use Illuminate\Http\Request;

/**
 * 高级项目协作与里程碑管理
 */
class ProjectManagementController extends Controller
{
    protected $projectService;

    public function __construct(ProjectManagementService $projectService)
    {
        $this->projectService = $projectService;
    }

    /**
     * @ApiTitle (创建项目)
     * @ApiSummary (创建新的协作项目)
     * @ApiMethod (POST)
     * @ApiRoute (/api/projects/create)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="name", type="string", required=true, description="项目名称")
     * @ApiParams (name="description", type="string", required=false, description="项目描述")
     * @ApiParams (name="type", type="string", required=true, description="项目类型：story/image/video/music/mixed")
     * @ApiParams (name="visibility", type="string", required=false, description="可见性：private/team/public")
     * @ApiParams (name="collaborators", type="array", required=false, description="协作者用户ID数组")
     * @ApiParams (name="settings", type="object", required=false, description="项目设置")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "项目创建成功",
     *   "data": {
     *     "project_id": 123,
     *     "name": "我的AI故事项目",
     *     "type": "story",
     *     "status": "active",
     *     "visibility": "private",
     *     "owner_id": 456,
     *     "collaborator_count": 3,
     *     "resource_count": 0,
     *     "created_at": "2024-01-01 12:00:00",
     *     "invite_code": "ABC123DEF"
     *   }
     * })
     */
    public function create(Request $request)
    {
        $rules = [
            'name' => 'required|string|min:2|max:100',
            'description' => 'sometimes|string|max:1000',
            'type' => 'required|string|in:story,image,video,music,mixed',
            'visibility' => 'sometimes|string|in:private,team,public',
            'collaborators' => 'sometimes|array|max:10',
            'collaborators.*' => 'integer|exists:users,id',
            'settings' => 'sometimes|array'
        ];

        $messages = [
            'name.required' => '项目名称不能为空',
            'name.min' => '项目名称至少2个字符',
            'name.max' => '项目名称不能超过100个字符',
            'type.required' => '项目类型不能为空',
            'type.in' => '项目类型必须是：story、image、video、music、mixed之一',
            'visibility.in' => '可见性必须是：private、team、public之一',
            'collaborators.max' => '最多支持10个协作者',
            'collaborators.*.exists' => '协作者用户不存在'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $projectData = [
            'name' => $request->name,
            'description' => $request->get('description'),
            'type' => $request->type,
            'visibility' => $request->get('visibility', 'private'),
            'collaborators' => $request->get('collaborators', []),
            'settings' => $request->get('settings', [])
        ];

        $result = $this->projectService->createProject($user->id, $projectData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (项目协作管理)
     * @ApiSummary (管理项目协作者和权限)
     * @ApiMethod (POST)
     * @ApiRoute (/api/projects/{id}/collaboration)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="项目ID")
     * @ApiParams (name="action", type="string", required=true, description="操作类型：invite/remove/update_role")
     * @ApiParams (name="user_id", type="int", required=true, description="目标用户ID")
     * @ApiParams (name="role", type="string", required=false, description="角色：viewer/editor/admin")
     * @ApiParams (name="permissions", type="array", required=false, description="权限数组")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "协作管理操作成功",
     *   "data": {
     *     "project_id": 123,
     *     "action": "invite",
     *     "user_id": 789,
     *     "role": "editor",
     *     "permissions": ["read", "write", "comment"],
     *     "invite_status": "sent",
     *     "updated_at": "2024-01-01 12:30:00"
     *   }
     * })
     */
    public function manageCollaboration($projectId, Request $request)
    {
        $rules = [
            'action' => 'required|string|in:invite,remove,update_role',
            'user_id' => 'required|integer|exists:users,id',
            'role' => 'sometimes|string|in:viewer,editor,admin',
            'permissions' => 'sometimes|array'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $collaborationData = [
            'action' => $request->action,
            'user_id' => $request->user_id,
            'role' => $request->get('role', 'viewer'),
            'permissions' => $request->get('permissions', [])
        ];

        $result = $this->projectService->manageCollaboration($projectId, $user->id, $collaborationData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (获取项目列表)
     * @ApiSummary (获取用户的项目列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/projects/list)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="type", type="string", required=false, description="项目类型过滤")
     * @ApiParams (name="status", type="string", required=false, description="状态过滤")
     * @ApiParams (name="role", type="string", required=false, description="角色过滤：owner/collaborator")
     * @ApiParams (name="page", type="int", required=false, description="页码")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "projects": [
     *       {
     *         "project_id": 123,
     *         "name": "我的AI故事项目",
     *         "type": "story",
     *         "status": "active",
     *         "role": "owner",
     *         "collaborator_count": 3,
     *         "resource_count": 15,
     *         "last_activity": "2024-01-01 12:00:00",
     *         "created_at": "2024-01-01 10:00:00"
     *       }
     *     ],
     *     "statistics": {
     *       "total_projects": 5,
     *       "owned_projects": 3,
     *       "collaborated_projects": 2,
     *       "active_projects": 4,
     *       "total_resources": 45
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 5,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function list(Request $request)
    {
        $rules = [
            'type' => 'sometimes|string|in:story,image,video,music,mixed',
            'status' => 'sometimes|string|in:active,archived,completed',
            'role' => 'sometimes|string|in:owner,collaborator',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $filters = [
            'type' => $request->get('type'),
            'status' => $request->get('status'),
            'role' => $request->get('role'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->projectService->getUserProjects($user->id, $filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (获取项目详情)
     * @ApiSummary (获取项目的详细信息)
     * @ApiMethod (GET)
     * @ApiRoute (/api/projects/{id}/detail)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="项目ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "project_id": 123,
     *     "name": "我的AI故事项目",
     *     "description": "一个关于未来的故事创作项目",
     *     "type": "story",
     *     "status": "active",
     *     "visibility": "private",
     *     "owner": {
     *       "user_id": 456,
     *       "username": "创作者123",
     *       "avatar": "https://example.com/avatar.jpg"
     *     },
     *     "collaborators": [
     *       {
     *         "user_id": 789,
     *         "username": "协作者1",
     *         "role": "editor",
     *         "permissions": ["read", "write", "comment"],
     *         "joined_at": "2024-01-01 11:00:00"
     *       }
     *     ],
     *     "resources": [
     *       {
     *         "resource_id": 101,
     *         "resource_type": "story",
     *         "title": "第一章",
     *         "status": "completed",
     *         "created_by": 456,
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "statistics": {
     *       "total_resources": 15,
     *       "completed_resources": 12,
     *       "in_progress_resources": 3,
     *       "total_collaborators": 3,
     *       "last_activity": "2024-01-01 15:30:00"
     *     },
     *     "settings": {
     *       "auto_backup": true,
     *       "version_control": true,
     *       "notification_enabled": true
     *     },
     *     "created_at": "2024-01-01 10:00:00",
     *     "updated_at": "2024-01-01 15:30:00"
     *   }
     * })
     */
    public function detail(Request $request, $projectId)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->projectService->getProjectDetail($projectId, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (更新项目)
     * @ApiSummary (更新项目信息和设置)
     * @ApiMethod (PUT)
     * @ApiRoute (/api/projects/{id})
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="项目ID")
     * @ApiParams (name="name", type="string", required=false, description="项目名称")
     * @ApiParams (name="description", type="string", required=false, description="项目描述")
     * @ApiParams (name="visibility", type="string", required=false, description="可见性")
     * @ApiParams (name="settings", type="object", required=false, description="项目设置")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "项目更新成功",
     *   "data": {
     *     "project_id": 123,
     *     "name": "更新后的项目名称",
     *     "description": "更新后的描述",
     *     "visibility": "team",
     *     "settings": {
     *       "auto_backup": true,
     *       "version_control": true
     *     },
     *     "updated_at": "2024-01-01 16:00:00"
     *   }
     * })
     */
    public function update($projectId, Request $request)
    {
        $rules = [
            'name' => 'sometimes|string|min:2|max:100',
            'description' => 'sometimes|string|max:1000',
            'visibility' => 'sometimes|string|in:private,team,public',
            'settings' => 'sometimes|array'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $updateData = $request->only(['name', 'description', 'visibility', 'settings']);

        $result = $this->projectService->updateProject($projectId, $user->id, $updateData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (删除项目)
     * @ApiSummary (删除项目及其相关数据)
     * @ApiMethod (DELETE)
     * @ApiRoute (/api/projects/{id})
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="项目ID")
     * @ApiParams (name="confirm", type="boolean", required=true, description="确认删除")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "项目删除成功",
     *   "data": {
     *     "project_id": 123,
     *     "deleted_resources": 15,
     *     "deleted_collaborations": 3,
     *     "deleted_at": "2024-01-01 16:30:00"
     *   }
     * })
     */
    public function delete($projectId, Request $request)
    {
        $rules = [
            'confirm' => 'required|boolean|accepted'
        ];

        $messages = [
            'confirm.required' => '必须确认删除操作',
            'confirm.accepted' => '必须确认删除操作'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->projectService->deleteProject($projectId, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
