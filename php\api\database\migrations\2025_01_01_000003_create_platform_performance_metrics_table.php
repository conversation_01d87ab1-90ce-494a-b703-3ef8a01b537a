<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('platform_performance_metrics', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('platform', 50)->comment('平台标识');
            $table->string('business_type', 50)->comment('业务类型');
            
            // 🔧 修正：精度符合规范
            $table->decimal('response_time_avg', 8, 3)->comment('平均响应时间(秒)');
            $table->decimal('success_rate', 5, 4)->comment('成功率');
            $table->decimal('cost_score', 5, 2)->comment('成本评分');
            $table->decimal('quality_score', 5, 2)->comment('质量评分');
            
            $table->integer('total_requests')->default(0)->comment('总请求数');
            $table->integer('failed_requests')->default(0)->comment('失败请求数');
            $table->decimal('uptime_percentage', 5, 2)->default(100.00)->comment('可用性百分比');
            $table->json('detailed_metrics')->nullable()->comment('详细指标');
            $table->date('metric_date')->comment('指标日期');
            $table->timestamps();

            // 索引优化
            $table->unique(['platform', 'business_type', 'metric_date'], 'uk_platform_business_date');
            $table->index(['platform', 'metric_date'], 'idx_platform_date');
            $table->index(['business_type', 'metric_date'], 'idx_business_date');
            $table->index('success_rate', 'idx_success_rate');
            $table->index('uptime_percentage', 'idx_uptime');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('platform_performance_metrics');
    }
};
